<?php

/**
 * <PERSON><PERSON> do uprawnień:
 * @@@@
 *
 * @package Modules
 * @subpackage Cron
 *
 */
class Cron extends Module
{

    private static $stanPortfela = 0;
    private static $activ = false;
    private static $statusy = array(1 => 'New', 3 => 'Denied', 4 => 'Pending', 99 => 'Completed');

    public function __construct($app)
    {
//        parent::__construct();
        $this->app = &$app;
        $this->app->SetDecorator('Null');
        $this->en = Database::activate();
        $this->urlparam = $this->app->urlparam;
        switch ($this->urlparam['a']) {

            case 'setcurrency':
                if ($_GET['b']) {
                    $this->setCurrenciesRates($_GET['b']);
                } else {
                    $this->setCurrenciesRates();
                }
                break;

            /**
             * Ustawienie prowizji, włączenie i wyłączenie produktów, cennik
             */
            case 'setoferta':
                $this->oferta_set();
                break;

            case 'rozliczosrodki':
                $data = false;
                if ($_GET['data'])
                    $data = $_GET['data'];
                $this->rozliczOsrodki($data);
                break;

            /**
             * Tygodniowe rozliczenie ośrodków
             */
            case 'rozliczosrodkiw':
                $data = false;
                if ($_GET['data']) {
                    $data = $_GET['data'];
                }
                $this->rozliczOsrodkiWeek($data);
                break;

            case 'rozliczpw':
                $data = false;
                if ($_GET['data']) {
                    $data = $_GET['data'];
                }
                $this->rozliczPW($data);
                break;

            case 'rozliczps':
                $data = false;
                if ($_GET['data']) {
                    $data = $_GET['data'];
                }
                $this->rozliczPS($data);
                break;

            /**
             * Rozliczenie Ośrodek -> partner WP
             */
            case 'rozliczwp':
                $data = false;
                if ($_GET['day']) {
                    $data = $_GET['day'];
                }
                $this->rozliczWP($data);
                break;

//            case 'getmail':
//                break;


            case 'checkcardstatus':
                require_once('application/admin_v4_0/modules/apibsproccess/AdminApibsproccess.php');
                $e = AdminAPIBSProccess::CheckCardsStatus();
                echo $e;
                die();
                break;

            case 'checkvoucherstatus':
                require_once('application/admin_v4_0/modules/apibsproccess/AdminApibsproccess.php');
                $e = AdminAPIBSProccess::CheckVoucherStatus();
                echo $e;
                die();
                break;

            case 'checkticketsstatus':
                require_once('application/admin_v4_0/modules/apibsproccess/AdminApibsproccess.php');
                $e = AdminAPIBSProccess::CheckTicketsStatus();
                echo $e;
                die();
                break;

            case 'processqueue':
                $t = time();
                $start = strtotime(date('Y-m-d') . " 04:59:00");
                $stop = strtotime(date('Y-m-d') . " 23:55:00");
                require_once('application/admin_v4_0/modules/apibsproccess/AdminApibsproccess.php');
                $api = new AdminAPIBSProccess($this->app);
                $l = $api->CronProcesQueue();
                echo $l . '<br>';
                $api->ProcessAnswers();
                break;

            case 'processanswers':
                require_once('application/admin_v4_0/modules/apibsproccess/AdminApibsproccess.php');
                $api = new AdminAPIBSProccess($this->app);
                break;

            case 'setinvoices':
                $start = strtotime(date('Y-m-d', strtotime('-1 day')) . ' 00:00:00');
                $zamoplacone = $this->en->select('SELECT z.*, f.numer_fv FROM ts_zamowienia z LEFT JOIN `ts_faktury` f on z.id = f.id_zamowienia WHERE z.data_platnosci > ' . $start . ' AND z.faktura = 1 AND f.numer_fv IS NULL');
                echo 'Zamówienia opłacone: ' . count($zamoplacone) . '<br>';
                $ile = count($zamoplacone);
                if ($ile > 0) {
                    $ll = 0;
                    foreach ($zamoplacone as $v) {
                        $resp = Faktury::getOrderInvoice($v['id'], false, true, '', true);
                        $nrfak = Faktury::$fnumer;
                        $this->LogInsert('Faktury', $v['id'],
                            'Wystawiono fakturę nr ' . $nrfak . ' dla zamówienia ' . $v['id']);
                        $ll++;
                        if ($ll > 5) {
                            die();
                        }
                    }
                }
                die();

                break;

            case 'setallinvoices':
                $start = strtotime('2015-10-01 00:00:00');
                $zamoplacone = $this->en->select('SELECT z.*, f.numer_fv FROM ts_zamowienia z LEFT JOIN `ts_faktury` f on z.id = f.id_zamowienia WHERE z.data_platnosci >' . $start . ' AND z.faktura = 1 AND f.numer_fv IS NULL');
                echo 'Zamówienia opłacone bez faktury: ' . count($zamoplacone) . '<br>';
                $ile = count($zamoplacone);
                echo '<br>Zamówienia do sprawdzenia: ' . $ile . '<br>';
                if ($ile > 0) {
                    $ll = 0;
                    foreach ($zamoplacone as $v) {
                        $resp = Faktury::getOrderInvoice($v['id'], false, true, '', true);
//                        var_dump($resp);
                        echo '<br>';
                        $ll++;
                        $nrfak = Faktury::$fnumer;
                        $this->LogInsert('Faktury', $v['id'],
                            'Wystawiono fakturę nr ' . $nrfak . ' dla zamówienia ' . $v['id']);
                        echo $ll;
                        if ($ll > 5) {
                            die();
                        }
                    }
                }
                die();
                break;

            case 'cancelorder':
                $seven = date('Y-m-d', time() - 7 * 24 * 60 * 60) . ' 00:00:00';
                echo $seven . '<br>';
                $stare = $this->en->select('SELECT id FROM ' . TABLE_PREFIX . 'zamowienia WHERE status=-1 AND data_platnosci = 0 AND ts <"' . $seven . '"');
                foreach ($stare as $v) {
                    Zamowienie::cancelOrder($v['id'], true);
                    echo $v['id'] . '<br>';
                    $this->LogInsert('Zamówienie', $v['id'], 'Usunięto zamówienie ' . $v['id']);
                }
                die();
                break;

            case 'removebaskets':
                $seven = date('Y-m-d', time() - 1 * 24 * 60 * 60);
                echo $seven . '<br>';
                $mgo = Mymongo::activate();
                $baskets = $mgo->getOldBaskets($seven);
                $removed = 0;
                foreach ($baskets as $basket) {
                    echo $basket['_id']['$oid'] . '<br>';
                    Zamowienie::releaseBlockedItems($basket['_id']['$oid']);
                    $mgo->removeDocument($basket['_id']['$oid'], 'basket');
                    $removed++;
                }
//                $mgo->removeOldBaskets($seven);
                echo 'Usunięto ' . $removed . ' koszyków' . '<br>';
                $this->LogInsert('BASKETS', 0, 'Usunięto koszyki starsze niż ' . $seven);
                die();
                break;


            case 'acceptrozliczenie':
                $data = date('Y-m-d H:i:s', strtotime('- 5 days'));
                $stare = $this->en->select('SELECT id FROM ' . TABLE_PREFIX . 'rozliczenia WHERE akceptacja=0 AND ts < "' . $data . '"');
                $ile = count($stare);
                echo "Found $ile items to accept<br>";
                if ($ile > 0) {
                    $time = 180 * (int) $ile;
                    ini_set('max_execution_time', $time);
                }
                foreach ($stare as $v) {
                    require_once 'application/admin_v4_0/modules/finance/AdminFinance.php';
                    AdminFinance::StaticAjaxAuthCheck($v['id'], false);
                    echo $v['id'] . '<br>';
                    $this->LogInsert('Rozliczenie', $v['id'], 'Automatyczna akceptacja rozliczenia ' . $v['id']);
                }
                die();
                break;

            case 'rozliczenieinvoices':
                $stare = $this->en->select('SELECT id, mgoid, idpartnera, nazwa FROM ' . TABLE_PREFIX . 'rozliczenia WHERE akceptacja=1 AND (faktura="" OR faktura IS NULL) AND autofaktura=1');
                $ile = count($stare);
                echo "Found $ile items to issue invoices<br>";
                if ($ile > 0) {
                    $time = 180 * $ile;
                    $met = ini_set('max_execution_time', $time);
                    echo $time . '<BR>';
                }
                foreach ($stare as $v) {
                    require_once 'application/website_v4_0/modules/partner/Partner.php';
                    $faktura = Partner::_prepareInvoice($v['mgoid']);
                    echo $v['mgoid'] . '<br>';
                    if (false === $faktura) {
                        $this->LogInsert('Rozliczenie', $v['id'], 'Błąd generowania faktury rozliczenia ' . $v['id']);
                        continue;
                    }
                    if (-1 === $faktura) {
                        $this->LogInsert('Rozliczenie', $v['id'], 'Ujemne wygenerowanie faktury rozliczenia ' . $v['id']);
                        Mailsend::SendMessage(
                            'Ujemne wygenerowanie faktury rozliczenia ' . $v['id'] ,
                            'Ujemne wygenerowanie faktury rozliczenia ' . $v['id'] . '. Partner: ' . $v['idpartnera'] . ', Okres: ' . $v['nazwa'],
                            '<EMAIL>',
                            MAILER_FROM,
                            MAILER_NAME,
                            MAILER,
                            unserialize(MAILER_CONF),
                        );
                        continue;
                    }
                    $this->en->update_rows('rozliczenia', array(array('faktura', $faktura, 'STRING')), array(array('mgoid', $v['mgoid'], 'STRING')));
                    $this->LogInsert('Rozliczenie', $v['id'], 'Automatyczne wygenerowanie faktury do rozliczenia ' . $v['id']);
                    $attachments = [[ 'plik' => $faktura, 'nazwa' => 'faktura_rozliczenia_' . $v['nazwa'] . '_' . $v['idpartnera'] . '.pdf' ]];
                    Partner::sendInvoiceEmail($v['nazwa'], $v['idpartnera'], false, $attachments);
                }
                die();
                break;

            case 'kuponyinvoices':
                $stare = $this->en->select('SELECT id, mgoid FROM ' . TABLE_PREFIX . 'rozliczenia WHERE akceptacja=1 AND faktura_kupony="1" AND autofaktura=1');
                $ile = count($stare);
                if ($ile > 0) {
                    $time = 13 * intval($ile);
                    $met = ini_set('max_execution_time', $time);
                    echo $time . '<BR>';
                }
                foreach ($stare as $v) {
                    require_once 'application/website_v4_0/modules/partner/Partner.php';
                    $faktura = Partner::_prepareInvoiceKupon($v['mgoid']);
                    echo $v['mgoid'] . '<br>';
                    if (false == $faktura) {
                        $this->LogInsert('Rozliczenie', $v['id'], 'Błąd generowania faktury za kupony rozliczenia ' . $v['id']);
                        continue;
                    }
                    $this->en->update_rows('rozliczenia', array(array('faktura_kupony', $faktura, 'STRING')), array(array('mgoid', $v['mgoid'], 'STRING')));
                    $this->LogInsert('Rozliczenie', $v['id'], 'Automatyczne wygenerowanie faktury za kupony do rozliczenia ' . $v['id']);
                }
                die();
                break;

            case 'ubezpwpinvoices':
                $stare = $this->en->select('SELECT id, mgoid FROM ' . TABLE_PREFIX . 'rozliczenia WHERE akceptacja=1 AND faktura = "n/a" AND faktura_kupony="" AND autofaktura=1');
                $ile = count($stare);
                if ($ile > 0) {
                    $time = 13 * intval($ile);
                    $met = ini_set('max_execution_time', $time);
                    echo $time . '<BR>';
                } else {
                    die('nothing');
                }
                foreach ($stare as $v) {
                    require_once 'application/website_v4_0/modules/partner/Partner.php';
                    $faktura = Partner::_prepareInvoiceUWP($v['mgoid']);
                    echo $v['mgoid'] . '<br>';
                    if (false == $faktura) {
                        $this->LogInsert('Rozliczenie', $v['id'], 'Błąd generowania faktury za kupony rozliczenia ' . $v['id']);
                        continue;
                    }
                    if ('n/a' == $faktura) {
                        $this->LogInsert('Rozliczenie', $v['id'], 'Brak sprzedaży ubezpieczeń przez WP, rozliczenie ' . $v['id']);
                        $this->en->update_rows('rozliczenia', array(array('faktura_kupony', $faktura, 'STRING')), array(array('mgoid', $v['mgoid'], 'STRING')));
                        echo $faktura . ' ' . $v['id'] . '<br>';
                        continue;
                    }
                    $this->en->update_rows('rozliczenia', array(array('faktura_kupony', $faktura, 'STRING')), array(array('mgoid', $v['mgoid'], 'STRING')));
                    $this->LogInsert('Rozliczenie', $v['id'], 'Automatyczne wygenerowanie faktury za kupony do rozliczenia ' . $v['id']);
                    echo $faktura . ' : rozliczenie ' . $v['id'] . '<br>';
                }
                die();
                break;

            case 'zuzyciebs':
                ini_set('memory_limit', '512M');
                ini_set('max_execution_time', '480');
                require_once('application/admin_v4_0/modules/apibsproccess/AdminApibsproccess.php');
                $api = new AdminAPIBSProccess($this->app);
                $process_start = time();
                if ($_GET['parentid'] != '') {
                    $mgo = Mymongo::activate();
                    $doc = $mgo->db->apibs_zuzycie->findOne(array('_id' => new MongoId($_GET['parentid'])));
                    $content = $doc['query'];
                } else {
                    if (0 === preg_match('/([1|2][0-9][0-9][0-9])-([0][1-9]|[1][0-2])-([0][1-9]|[1|2][0-9]|[3][0-1])/', $_GET['basedate'])) {
                        $date = date('Y-m-d', time());
                    } else {
                        $date = $_GET['basedate'];
                    }
                    if (0 === preg_match('/([1|2][0-9][0-9][0-9])-([0][1-9]|[1][0-2])-([0][1-9]|[1|2][0-9]|[3][0-1])/', $_GET['finishdate'])) {
                        $datee = $date;
                    } else {
                        $datee = $_GET['finishdate'];
                    }

                    switch ($_GET['part']) {
                        case 2:
                            $start = $date . ' 00:00:00';
                            $stop = $datee . ' 03:59:59';
                            break;
                        case 3:
                            $start = $date . ' 04:00:00';
                            $stop = $datee . ' 09:59:59';
                            break;
                        case 4:
                            $start = $date . ' 10:00:00';
                            $stop = $datee . ' 11:59:59';
                            break;
                        case 5:
                            $start = $date . ' 12:00:00';
                            $stop = $datee . ' 13:59:59';
                            break;
                        case 6:
                            $start = $date . ' 14:00:00';
                            $stop = $datee . ' 15:59:59';
                            break;
                        case 7:
                            $start = $date . ' 16:00:00';
                            $stop = $datee . ' 19:59:59';
                            break;
                        default:
                            $start = $date . ' 20:00:00';
                            $stop = $datee . ' 23:59:59';
                            break;
                    }
                    echo "start: $start, stop: $stop<br>";
                    $time_start = time();
                    $doc = $this->getZuzycieBS($start, $stop);
                    $time_mongo = time();
                    $content = $doc['query'];
                }
                $api->answer_context_1m($doc['answer'], $content, false, true);
                $time_stop = time();
                echo "<br>Start procesu: " . date('Y-m-d H:i:s', $process_start) . ", zakańczony zapis do mgo: " . date('Y-m-d H:i:s', $time_mongo) . ", zakończony proces: " . date('Y-m-d H:i:s', $time_stop);
                echo "<br>Czas do zapisu mgo: " . ($time_mongo - $time_start) . ", czas przetwarzania odpowiedzi: " . ($time_stop - $time_mongo);
                break;


            case 'getzuzyciebs':
                $this->getZuzycieBS();
                break;

            case 'zuzyciect':
                require_once('application/admin_v4_0/modules/apibsproccess/AdminApibsproccess.php');
                if (0 === preg_match('/([1|2][0-9][0-9][0-9])-([0][1-9]|[1][0-2])-([0][1-9]|[1|2][0-9]|[3][0-1])/', $_GET['basedate'])) {
                    $date = date('Y-m-d', time());
                } else {
                    $date = $_GET['basedate'];
                }
                $res = AdminApibsproccess::updateContinTickets($date);
                echo 'RES: ' . $res . '<br>';
                break;

            // aktualizacja statusów karnetów wg zuzycia (ustawienia 0 dla karnetów bez dni do uzycia
            case 'updatets':
                require_once('application/admin_v4_0/modules/apibsproccess/AdminApibsproccess.php');
                $res = AdminApibsproccess::updateTicketStatus();
                echo 'RES: ' . $res . '<br>';
                break;

            case 'syncbs':
                require_once('application/admin_v4_0/modules/apibsproccess/AdminApibsproccess.php');
                $api = new AdminApibsproccess($this->app);
                $api->SynchoBS();
                break;

            case 'dosyncbs':
                require_once('application/admin_v4_0/modules/apibsproccess/AdminApibsproccess.php');
                $api = new AdminApibsproccess($this->app);
                $api->DoSynchoBS();
                break;

            case 'resendvouchers':
                return;
                $start = new DateTime();
                $licznik = 0;
//                $this->en->query('UPDATE '.TABLE_PREFIX.'vouchery SET status = -1 WHERE installation = 208');
                $vouchery = $this->en->select('SELECT * FROM ' . TABLE_PREFIX . 'vouchery WHERE installation = 208');
                $olidz = 0;
                foreach ($vouchery as $voucher) {
                    if ($olidz !== $voucher['id_zamowienia']) {
                        $adres = Zamowienie::PrepareAdres2Production($voucher['id_zamowienia']);
                        $olidz = $voucher['id_zamowienia'];
                    }
                    Voucher::send2production($voucher['id'], 1, $voucher, $adres);
                    $licznik++;
                }
                $finish = new DateTime();
                echo 'Rekordy: ' . $licznik . '<br>';
                echo 'Czas: ' . $finish->diff($start)->format('%I:%S') . '<br>';

                break;

            case 'mkrozpdf':
                $this->MkRozliczeniePDF(intval($_GET['rid']));
                break;


            case 'updatetid':

                $datareg = '/([1|2][0-9][0-9][0-9])-([0][1-9]|[1][0-2])-([0][1-9]|[1|2][0-9]|[3][0-1])/';
                if (0 === preg_match($datareg, $_GET['start']) || 0 === preg_match($datareg, $_GET['stop'])) {
                    return;
                }
                $start = $_GET['start'];
                $stop = $_GET['stop'];
                $mgo = Mymongo::activate();
                $dates = Additional::DateRangeArray($start, date('Y-m-d', strtotime($stop . ' + 1 day')), false, 'P1D');
                $counter = 0;
                $sql = 'SELECT serial FROM ' . TABLE_PREFIX . 'karty WHERE status = 1 AND serial <>"nowa"';
                $activeCards = array_column($this->en->select($sql), 'serial');
                $pominiete = 0;
//                var_dump($activeCards);
                foreach ($dates as $date) {
                    $where = array(
                        'data_start' => array(
                            '$regex' => new MongoRegex('/' . $date . '/')
                        ),
                        'answer.HISTORIA.TYPREJ_I' => '48'
                    );
                    $docs = $mgo->db->apibs_zuzycie->find($where);
                    foreach ($docs as $doc) {
                        $adoc = $mgo->Mgo2array($doc);
                        if (!$adoc['answer']) {
                            continue;
                        }
                        if (!$adoc['answer']['HISTORIA'] OR is_null($adoc['answer']['HISTORIA'])) {
                            continue;
                        }
                        foreach ($adoc['answer']['HISTORIA'] as $event) {
                            if ($event['TYPREJ_I'] != '48') {
                                continue;
                            }
                            if ($event['TID'] == '') {
                                continue;
                            }
                            $karta = $event['KARTA'];
                            if (false === in_array($karta, $activeCards)) {
                                $pominiete++;
                                continue;
                            }
                            $kartareg = '/^3[\d]{14}$/';
                            if (1 === preg_match($kartareg, $karta)) {
                                $idreg = '/^3[0]+(\d+)$/';
                                preg_match($idreg, $karta, $match);
                                $id = $match[1];
                                $sql = 'UPDATE ' . TABLE_PREFIX . 'karnety SET tid = "' . $event['TID'] . '" WHERE status = 1 AND bsid="' . $event['BILETNR'] . '" AND id_karty=' . $id . ' AND data_wydania="' . $event['DATA'] . '"';
                            } else {
                                $sql = 'UPDATE ' . TABLE_PREFIX . 'karnety SET tid = "' . $event['TID'] . '" WHERE status = 1 AND bsid="' . $event['BILETNR'] . '" AND serial_karty="' . $event['KARTA'] . '" AND data_wydania="' . $event['DATA'] . '"';
                            }
                            echo $sql . '<br>';
                            $this->en->query($sql);
                            $counter++;
                        }
                    }
                }
                echo 'zapytań: ' . $counter . '<br>';
                echo 'pominiete: ' . $pominiete . '<br>';
                break;

            case 'updatetidc':
                echo 'in<br>';
                $ordersql = "select apia.*, apirp.prid from api_answers apia left join api_queue apiq on apiq.idop = apia.idop LEFT JOIN api_rozkaz_produkt apirp ON apirp.idop = apia.idop WHERE apia.status = 1 AND apiq.task = '1c' AND apirp.ptype = 'karnet' AND apirp.ts > '2018-01-01'";
                $orders = $this->en->select($ordersql);
                $updated = array();
                echo count($orders) . '<br>';
                foreach ($orders as $order) {
                    $content = unserialize(base64_decode($order['content']));
                    if ($content['TID'] == '1' OR $content['TID'] == '' || false === isset($content['TID'])) {
                        continue;
                    }
                    $karnet = $this->en->select_r('SELECT * FROM ' . TABLE_PREFIX . 'karnety where id = ' . $order['prid']);
                    if (count($karnet) == 0) {
                        continue;
                    }
                    if ($content['TID'] != $karnet['tid']) {
                        $sql = 'Update ' . TABLE_PREFIX . 'karnety SET tid ="' . $content['TID'] . '" WHERE id = ' . $karnet['id'];
                        echo $sql . '<br>';
                        $this->en->query($sql);
                        $updated[] = array(
                            'old' => $karnet,
                            'new' => $content
                        );
                    }
                }
                echo count($updated) . '<br>';
                break;

            default:
                $this->output = false;
        }
    }

    public function getZuzycieBS($start = 0, $stop = 0)
    {
        ini_set('memory_limit', '512M');
        ini_set('max_execution_time', '480');
        $t1 = time();
        if ($start != 0 AND $stop != 0) {
            $vars['data_start'] = $start;
            $vars['data_koniec'] = $stop;
        } else {
            if (0 === preg_match('/([1|2][0-9][0-9][0-9])-([0][1-9]|[1][0-2])-([0][1-9]|[1|2][0-9]|[3][0-1])/', $_GET['basedate'])) {
                $date = date('Y-m-d', time());
            } else {
                $date = $_GET['basedate'];
            }
            if (0 === preg_match('/([1|2][0-9][0-9][0-9])-([0][1-9]|[1][0-2])-([0][1-9]|[1|2][0-9]|[3][0-1])/', $_GET['finishdate'])) {
                $datee = $date;
            } else {
                $datee = $_GET['finishdate'];
            }
            $vars['data_start'] = $date . ' 00:00:01';
            $vars['data_koniec'] = $datee . ' 23:59:59';
        }
        // prawdziwy start
        $vars['card'] = '0';
        require_once('application/admin_v4_0/modules/apibsproccess/AdminApibsproccess.php');
        $api = new AdminAPIBSProccess($this->app);
        $allhistory = array();
        $periodc = 1;
        $periods = Additional::splitStartStopTime(
            $vars['data_start'], $vars['data_koniec'],
            Additional::DateRangeArray($vars['data_start'], $vars['data_koniec'], false, 'PT1H', 'Y-m-d H:i:s')
        );
        $mgo = Mymongo::activate();
        foreach ($periods as $period) {
            $doc = array();
            $history = array();
            $end = false;
            $l = 1;
            $pstart = $period['start'];
            $cstart = $period['start'];
            $cstop = $period['stop'];

            do {
                $idop = APIBS::GenerateIDOP();
                $content['OPERACJA'] = '1mv20';
                $content['IDOP'] = (string)$idop;
                $content['KARTA'] = $vars['card'];
                $content['DATACZASP'] = $cstart;
                $content['DATACZASK'] = $cstop;
                $this->en->query(sprintf("INSERT INTO " . API_TABLE_PREFIX . "queue(`idop`, `data`, `task`, `content`, `status`, `orderid`, `indeks`) VALUES(%d, %d, '%s', '%s', %d, %d, %d)",
                    $idop, time(), '1mv20', $this->en->Escape(base64_encode(serialize($content))), 1, 0, 0));
                $result = APIBS::reqpost($api->apiurl, json_encode($content), $api->apiport);
                if (false === $result) {
                    $this->app->LogInsert('APIBSCE', 0, 'ProcessQueue: Błąd połaczenia z serwerem BS, op: ' . $idop);
                    $subject = 'Błąd połączenia z serwerem BS, mod: CRON, getzuzyciebs';
                    $body = 'Błąd połączenia z serwerem BS, mod: CRON, getzuzyciebs, data: ' . date('Y-m-d H:i:s');
                    $rec = '<EMAIL>';
                    $sender = '<EMAIL>';
                    Mailsend::SendMessage($subject, $body, $rec, $sender, MAILER_NAME, MAILER, unserialize(MAILER_CONF), array());
                    $rec = '<EMAIL>';
                    Mailsend::SendMessage($subject, $body, $rec, $sender, MAILER_NAME, MAILER, unserialize(MAILER_CONF), array());
                    return false;
                } else {
                    $res = json_decode($result[0], true);
                    $history = array_merge($history, $res['HISTORIA']);
                    $top = count($history);
                    if ($history[$top - 1]['DATA'] == '1900-01-01 00:00:00') {
                        $l++;
                        $cdate = new DateTime($history[$top - 2]['DATA']);
                        $cdate->add(new DateInterval('PT1S'));
                        $cstart = $cdate->format('Y-m-d H:i:s');
                        array_pop($history);
                        unset($result);
                    } else {
                        $res['HISTORIA'] = $history;
                        $allhistory = array_merge($allhistory, $history);
                        $end = true;
                    }
                    $this->en->query(sprintf("INSERT INTO " . API_TABLE_PREFIX . "answers(`idop`, `content`, `data` , `status`) VALUES(%d, '%s', %d, %d)",
                        $idop, $this->en->Escape(base64_encode(serialize($res))), time(), 1));
                    $ans['reqpost'] = $result[0];
                    $ans['cgetinfo'] = $result[1];
                    $ans['query'] = $content;
                    $ans['answer'] = json_decode($result[0], true);
                    $ans['data_wyslania'] = date('Y-m-d H:i:s');
                    $mgo->saveOffer($ans, true, 'apibs');
                    unset($doc);
                    unset($ans);
                }
            } while ($end === false);
            $doc['query'] = $content;
            $doc['answer'] = $res;
            $doc['cgetinfo'] = $result[1];
            $doc['karta'] = $vars['card'];
            $doc['licznik'] = $l;
            $doc['data_start'] = $pstart;
            $doc['data_koniec'] = $cstop;
            $doc['data_wyslania'] = date('Y-m-d H:i:s');
            $doc['ttime'] = time() - $t1;
            $mid = $mgo->saveOffer($doc, true, 'apibs_zuzycie');
        }
        $doc['answer']['HISTORIA'] = $allhistory;
        return $doc;
    }

    public function oferta_set()
    {
        $this->mkobroty();
        $this->mkprowizja();
        $this->mkpricelist();
    }

    public function rozliczOsrodki($okres = false)
    {
        if (0 === (int)preg_match('/([1|2][0-9][0-9][0-9])-([0][1-9]|[1][0-2])/', $okres)) {
            $okres = date('Y-m', strtotime(' -1 month'));
        }
        $dataod = $okres . '-01 00:00:01';
        $datado = $okres . '-' . date('t', strtotime($dataod)) . ' 23:59:59';


        $sql = 'SELECT DISTINCT zk.id_platnika '
            . 'FROM ' . TABLE_PREFIX . 'zamowienia_koszty zk '
            . 'LEFT JOIN ' . TABLE_PREFIX . 'partners pa ON zk.id_platnika = pa.id '
            . 'LEFT JOIN ' . TABLE_PREFIX . 'partners_meta pm ON zk.id_platnika = pm.id_parent AND pm.current = 1 AND pm.properties = "partner_wlasne_rozliczenia" '
            . 'WHERE zk.data_platnosci between ' . strtotime($dataod) . ' AND ' . strtotime($datado) . ' AND pa.partnertype = 2 AND pm.value IS NULL';
        $osrodki = $this->en->select($sql);
        if (count($osrodki) > 0) {
            $roz = new Rozliczenia();
        }
        foreach ($osrodki as $v) {
            if ($v['id_platnika'] == 0) {
                continue;
            }
            echo $v['id_platnika'] . '<br>';
            $w = $roz->RozliczOsrodek($v['id_platnika'], $okres);
            if (false === $w) {
                $this->LogInsert('CRON: Roz ośr', $v['id_platnika'], 'Błąd rozliczenia ośrodka');
            } else {
                $this->LogInsert('CRON: Roz ośr', $v['id_platnika'], 'Komunikat ' . $w);
            }
            $roz->resetRozliczenie();
//            var_dump($w);
        }
    }

    public function rozliczOsrodkiWeek($start = false, $dlugosc = 7)
    {
        if (0 === intval(preg_match('/([1|2][0-9][0-9][0-9])-([0][1-9]|[1][0-2])-([0][1-9]|[1][0-9]|[2][0-9]|[3][0-1])/',
                $start))) {
            $st = new DateTime();
            $st->sub(new DateInterval('P' . $dlugosc . 'D'));
            $start = $st->format('Y-m-d');
        }
        $ed = new DateTime($start);
        $ed->add(new DateInterval('P' . ($dlugosc - 1) . 'D'));
        $datado = $ed->format('Y-m-d');
        $dataod = $start;

//        $int = $st->diff($ed)->days+2;
//        die(var_dump($dataod, $datado,$int));

        $sql = 'SELECT DISTINCT zk.id_platnika '
            . 'FROM ' . TABLE_PREFIX . 'zamowienia_koszty zk LEFT JOIN ' . TABLE_PREFIX . 'partners pa ON zk.id_platnika = pa.id '
            . 'WHERE zk.data_platnosci between ' . strtotime($dataod . ' 00:00:01') . ' AND ' . strtotime($datado . ' 23:59:59') . ' AND pa.partnertype = 2';
        $osrodki = $this->en->select($sql);
        if (count($osrodki) > 0) {
            $roz = new Rozliczenia();
        }
        foreach ($osrodki as $v) {
            if ($v['id_platnika'] == 0) {
                continue;
            }
            echo $v['id_platnika'] . '<br>';
            $w = $roz->RozliczOsrodekWeek($v['id_platnika'], $dataod, $datado);
            if (false === $w) {
                $this->LogInsert('CRON: Rozliczenie ośrodka', $v['id_platnika'], 'Błąd rozliczenia ośrodka');
            } else {
                $this->LogInsert('CRON: Rozliczenie ośrodka', $v['id_platnika'], 'Komunikat ' . $w);
            }
            $roz->resetRozliczenie();

        }
    }

    /**
     * Rozliczenia produktu wirtualnego
     * @param type $okres
     */
    public function rozliczPW($okres = false)
    {
        if (0 === intval(preg_match('/([1|2][0-9][0-9][0-9])-([0][1-9]|[1][0-2])/', $okres))) {
            $okres = date('Y-m', strtotime(' -1 month'));
        }
        $dataod = $okres . '-01 00:00:01';
        $datado = $okres . '-' . date('t', strtotime($dataod)) . ' 23:59:59';

        $sql = 'SELECT DISTINCT zk.id_platnika '
            . 'FROM ' . TABLE_PREFIX . 'zamowienia_koszty zk LEFT JOIN ' . TABLE_PREFIX . 'partners pa ON zk.id_platnika = pa.id '
            . 'WHERE zk.data_platnosci between ' . strtotime($dataod) . ' AND ' . strtotime($datado) . ' AND pa.partnertype = 9';
        $osrodki = $this->en->select($sql);
        if (count($osrodki) > 0) {
            $roz = new Rozliczenia();
        }
        foreach ($osrodki as $v) {
            if ($v['id_platnika'] == 0) {
                continue;
            }
            echo $v['id_platnika'] . '<br>';
            $w = $roz->RozliczVprodukt($v['id_platnika'], $okres);
            if (false === $w) {
                $this->LogInsert('CRON: Rozliczenie WirtualnyProdukt', $v['id_platnika'], 'Błąd rozliczenia ośrodka');
            } else {
                $this->LogInsert('CRON: Rozliczenie WirtualnyProdukt', $v['id_platnika'], 'Komunikat ' . $w);
            }
            $roz->resetRozliczenie();
//            var_dump($w);
        }
    }

    /**
     * Rozliczenia partnera sprzedazowego
     * @param type $okres
     */
    public function rozliczPS($okres = false)
    {
        if (0 === intval(preg_match('/([1|2][0-9][0-9][0-9])-([0][1-9]|[1][0-2])/', $okres))) {
            $okres = date('Y-m', strtotime(' -1 month'));
        }
        $dataod = $okres . '-01 00:00:01';
        $datado = $okres . '-' . date('t', strtotime($dataod)) . ' 23:59:59';

        $sql = 'SELECT DISTINCT zk.id_platnika '
            . 'FROM ' . TABLE_PREFIX . 'zamowienia_koszty zk LEFT JOIN ' . TABLE_PREFIX . 'partners pa ON zk.id_platnika = pa.id '
            . 'WHERE zk.data_platnosci between ' . strtotime($dataod) . ' AND ' . strtotime($datado) . ' AND pa.partnertype = 1';
        $osrodki = $this->en->select($sql);
        if (count($osrodki) > 0) {
            $roz = new Rozliczenia();
        }
        foreach ($osrodki as $v) {
            if ($v['id_platnika'] == 0) {
                continue;
            }
            echo $v['id_platnika'] . '<br>';
            $w = $roz->RozliczSprzedaz($v['id_platnika'], $okres);
            if (false === $w) {
                $this->LogInsert('CRON: Rozliczenie Partner sprzedażowy', $v['id_platnika'],
                    'Błąd rozliczenia ośrodka');
            } else {
                $this->LogInsert('CRON: Rozliczenie Partner sprzedażowy', $v['id_platnika'], 'Komunikat ' . $w);
            }
            $roz->resetRozliczenie();
//            var_dump($w);
        }
    }

    public function rozliczAll($okres = false)
    {
        if (0 === intval(preg_match('/([1|2][0-9][0-9][0-9])-([0][1-9]|[1][0-2])/', $okres))) {
            $okres = date('Y-m', strtotime(' -1 month'));
        }
        $dataod = $okres . '-01 00:00:01';
        $datado = $okres . '-' . date('t', strtotime($dataod)) . ' 23:59:59';

        $sql = 'SELECT DISTINCT zk.id_platnika '
            . 'FROM ' . TABLE_PREFIX . 'zamowienia_koszty zk LEFT JOIN ' . TABLE_PREFIX . 'partners pa ON zk.id_platnika = pa.id '
            . 'WHERE zk.data_platnosci between ' . strtotime($dataod) . ' AND ' . strtotime($datado) . ' AND pa.partnertype = 2';
        $osrodki = $this->en->select($sql);
        if (count($osrodki) > 0) {
            $roz = new Rozliczenia();

            foreach ($osrodki as $v) {
                if ($v['id_platnika'] == 0) {
                    continue;
                }
                echo $v['id_platnika'] . '<br>';
                $w = $roz->RozliczOsrodek($v['id_platnika'], $okres);
                if (false === $w) {
                    $this->LogInsert('CRON: Rozliczenie ośrodka', $v['id_platnika'], 'Błąd rozliczenia ośrodka');
                } else {
                    $this->LogInsert('CRON: Rozliczenie ośrodka', $v['id_platnika'], 'Komunikat ' . $w);
                }
                $roz->resetRozliczenie();
//                var_dump($w);
            }
        }
    }

    /**
     * Aktualizuje wartości walut
     * @param string $provider Nazwa dostawcy tabeli walut. Musi być utworzony sterownik do obsługi każdego dostawcy
     */
    private function setCurrenciesRates($provider = 'NBP')
    {
        $provider = strtoupper($provider);
        $cur = $this->{'CP_' . $provider}();
        if(empty($cur)) {
            echo "empty $provider curriences array";
            return;
        }
//        die(var_dump($cur));
        $rdate = $cur['data_publikacji'] ? $cur['data_publikacji'] : date('Y-m-d', time());
        $this->en->delete('currency_rates', array('rate_date' => $rdate));
        foreach ($this->app->currencies as $v) {
            if ($v['currency'] == 'PLN') {
                continue;
            }
            $rate = floatval($cur[$v['currency']]);
            $sql = 'update ' . TABLE_PREFIX . 'currency SET rate=' . $rate . ' WHERE currency="' . $v['currency'] . '"';
            if (false === $this->en->query($sql)) {
                echo 'false: ' . $v['currency'];
                die();
            }
            $sql1 = 'INSERT INTO ' . TABLE_PREFIX . 'currency_rates SET symbol = "' . $v['currency'] . '", rate = ' . $rate . ', provider="' . $provider . '", rate_date = "' . $rdate . '"';
            $this->en->query($sql1);
        }
//        var_dump($cur);
//        die();
        echo 'OK';
    }

    /**
     * Pobiera wiadomości dotyczące mediacji ze skrzyniki pocztowej (co godzinę?)
     */
    protected function Mail()
    {
        try {
            $email = new EmailReader;
            $email->SetServer(MEDIATION_EMAIL_SERVER);
            $email->SetLogin(MEDIATION_EMAIL);
            $email->SetPwd(MEDIATION_EMAIL_PASS);
            $maile = $email->GetEmails();
            $mongo = Mymongo::activate();
            $count = 0;
            foreach ($maile as $mail) {
                //  Sprawdza temat wiadomości, wymagany ciąg znaków [ID:id_ticket(int)]
                if (preg_match('(\[ID:\d+\])', $mail['subject'], $match)) {
                    $sprefix = $match[0];
                    preg_match('(\d+)', $sprefix, $match);
                    $id_ticket = $match[0];
                    $date = strtotime($mail['data']);
                    $sprawa = $mongo->case_list(1, false, $id_ticket, true);
                    $hash = md5($mail['deleteHash'] . $date);
                    if ($sprawa) {
                        $added = false;
                        //  Sprawdzanie czy wiadomość była już dodana
                        if (is_array($sprawa['wiadomosci'])) {
                            foreach ($sprawa['wiadomosci'] as $wiadomosc) {
                                if ($wiadomosc['hash'] == $hash) {
                                    $added = true;
                                    $email->DeleteEmail($mail['deleteHash']);
                                    break;
                                }
                            }
                        }

                        if (!$added) {
                            $sprawa['ts'] = time();
                            $dane_klienta = Mediation::ClientData($mail['from']);

                            if ($sprawa['wiadomosci']) {
                                $last_msg = end($sprawa['wiadomosci']);
                            } else {
                                $last_msg['id'] = 0;
                            }

                            $id_new_msg = $last_msg['id'] + 1;
                            $receiver_id = array();
                            $zalacznik = array();
                            $subject = substr($mail['subject'], strpos($mail['subject'], $sprefix) + strlen($sprefix));

                            //  Pobranie załączników z maili
                            if ($mail['attachments']) {
                                foreach ($mail['attachments'] as $attachment) {
                                    if ($attachment['name'] && $attachment['attachment']) {
                                        //  Ścieżka pliku
                                        $filename = 'data/4a0e3a2c7112947b72fbb8b866686872/' . $attachment['name'];
                                        //  Zapisanie pliku i pobranie wielkości
                                        $size = file_put_contents($filename, $attachment['attachment']);
                                        //  Pobranie danych nagłówka
                                        $file_header = get_headers(SITE_URL . $filename, 1);
                                        //  Dodanie dodatkowych danych przy zapisywaniu do bazy mongo
                                        $metadata['id_ticket'] = $id_ticket;
                                        $metadata['id_wiadomosci'] = $id_new_msg;
                                        //  Zapisanie załącznika do bazy plików mongo
                                        $id_zalacznik = $mongo->upload_file($filename, $metadata, true);
                                        $id_zalacznik = get_object_vars($id_zalacznik);
                                        //  Stworzenie tablicy z informacjami o pliku do danych sprawy
                                        $zalacznik[] = array(
                                            'id' => $id_zalacznik['$id'],
                                            'typ' => $file_header['Content-Type'],
                                            'nazwa' => $attachment['name'],
                                            'rozmiar' => Additional::formatSizeUnits($size)
                                        );
                                        //  Usunięcie pliku z serwera
                                        unlink($filename);
                                    }
                                }
                            }

                            foreach ($sprawa['prowadzacy'] as $leader) {
                                $receiver_id[] = array(
                                    'id' => $leader,
                                    'przeczytana' => 1,
                                );
                            }

                            $nowa_wiadomosc = array(
                                'id' => $id_new_msg,
                                'status' => 1,
                                'typ_nadawcy' => 'client',
                                'id_nadawcy' => $dane_klienta['id_klienta'],
                                'dane_nadawcy' => $dane_klienta['dane_klienta'],
                                'email_nadawcy' => $dane_klienta['email_klienta'],
                                'id_odbiorcy' => $sprawa['prowadzacy'],
                                'dane_odbiorcy' => 'Admin',
                                'email_odbiorcy' => MEDIATION_EMAIL,
                                'temat' => $subject,
                                'tresc' => $mail['body'],
                                'data_dodania' => date('Y-m-d H:i:s', $date),
                                'zalacznik' => $zalacznik
                            );

                            $sprawa['wiadomosci'][] = $nowa_wiadomosc;
                            $id_mongo = $sprawa['_id']['$id'];
                            unset($sprawa['_id']);
                            $mongo->updateDocument($sprawa, $id_mongo, 'case');
                            $email->DeleteEmail($mail['deleteHash']);

                            //  Dodanie notyfikacji
                            foreach ($sprawa['prowadzacy'] as $prowadzacy) {
                                $rec = $prowadzacy;
                                $module = 'mediation';
                                $item = 'admin.php?mediation&a=show&id_ticket=' . $id_ticket;
                                $mes = 'Nowa wiadomość w mediacji';
                                Sms::Send($rec, $module, $item, $mes);
                            }
                            $count++;
                        }
                    }
                }
            }
        } catch (Exception $e) {
            print $e->getMessage();
            die();
        }

        die('Dodano wiadomosci: ' . $count);
    }

    /**
     * Generuje rozliczenia dla Ośrodek - partner WP
     * @param type $day
     */
    function rozliczWP($day = false)
    {
        if ($day) {
            $date = $day;
        } else {
            $date = date('Y-m-d', strtotime('-1 day'));
        }
        $pat = '/^\d{4}-\d{2}-\d{2}$/';
        if (1 !== preg_match($pat, $date)) {
            die('Bad date');
        }
        require_once 'application/admin_v4_0/modules/finance/reportsOsrodek.php';
        $dataod = $date . ' 00:00:00';
        $datado = $date . ' 23:59:59';
        $sql = 'SELECT DISTINCT zk.id_platnika '
            . 'FROM ' . TABLE_PREFIX . 'zamowienia_koszty zk LEFT JOIN ' . TABLE_PREFIX . 'partners pa ON zk.id_platnika = pa.id '
            . 'WHERE zk.data_platnosci between ' . strtotime($dataod) . ' AND ' . strtotime($datado) . ' AND pa.partnertype = 1';
        $osrodki = $this->en->select($sql);
        if (count($osrodki) == 0) {
            die('No data');
        }
        require_once 'application/website_v4_0/modules/partner/Partner.php';
        $mailer = new Rozliczenia();
        foreach ($osrodki as $os) {
            list($year, $mc, $dday) = explode('-', $date);
            $test = $this->en->get_field_val('rozliczenia_partner_wp', 'id', array(
                array('idpartnera', $os['id_platnika'], 'INT'),
                array('rok', $year, 'INT'),
                array('miesiac', $mc, 'INT'),
                array('dzien', $dday, 'INT')
            ));
            if (0 !== intval($test)) {
                echo 'Rozliczenie powtórzone: mid: ' . $os['id_platnika'] . ', data: ' . $date;
                continue;
            }
            $roz = Reports4Osrodek::tabelaRozliczZamowieniaWP($os['id_platnika'], $date);
            if ($roz['status'] == 'OK') {
                $faktura = Partner::_prepareInvoiceWP($roz['mgoid']);
                $sql = 'UPDATE ' . TABLE_PREFIX . 'rozliczenia_partner_wp SET nrfaktury ="' . $this->en->Escape($faktura['nrfaktury']) . '", linkfaktury = "/' . $this->en->Escape($faktura['plik']) . '" WHERE id = ' . $roz['id'];
                $this->en->query($sql);
                $ret['status'] = 'OK';
                $ret['linkfaktury'] = $faktura['plik'];
                $ret['nrfaktury'] = $faktura['nrfaktury'];
                $ret['rozid'] = $roz['id'];
                $this->LogInsert('CRON: Rozliczenie Partner sprzedażowy', $os['id_platnika'], 'Rozliczenie dzienne ');
                echo json_encode($ret);
                $ret = array();
                $mailer->sendEmail($date, $os['id_platnika'], '34_invoice_ready', false);
            } else {
                $this->LogInsert('CRON: Rozliczenie Partner sprzedażowy', $os['id_platnika'], 'Błąd rozliczenia PSP');
            }
            $roz = array();
        }
        die();
    }


    /**
     * CurrencyProvider (CP) ECB (European Central Bank)
     * Base Currency EUR
     * @return array $a['USD']=1.234
     */
    private function CP_ECB()
    {
        $XML = simplexml_load_string(file_get_contents("http://www.ecb.europa.eu/stats/eurofxref/eurofxref-daily.xml"));
        if (false === $XML) {
            return array();
        }
        $cur = array();
        $cur['data_publikacji'] = (string)$XML->Cube->Cube['time'];
        foreach ($XML->Cube->Cube->Cube as $rate) {
            $cur[(string)$rate["currency"]] = (string)$rate["rate"];
        }
        return $cur;
    }

    /**
     * CurrencyProvider (CP) NBP (Narodowy Bank Polski)
     * Base Currency PLN
     * @return array $a['USD']=1.234
     */
    private function CP_NBP()
    {
        $XML = simplexml_load_string(file_get_contents("http://www.nbp.pl/kursy/xml/LastA.xml"));
//        die(var_dump($XML));
        if (false === $XML) {
            return array();
        }
        $cur = array();
        $cur['data publikacji'] = (string)$XML->data_publikacji;
        foreach ($XML->pozycja as $rate) {
            $kurs = 1 / (str_replace(',', '.', (string)$rate->kurs_sredni));
            $cur[(string)$rate->kod_waluty] = $kurs;
//            $cur[(string) $rate->kod_waluty] = str_replace(',','.',(string) $rate->kurs_sredni);
        }
        return $cur;
    }

    private function mkobroty()
    {
        Tools::AgregateIncome();
        echo 'done...0AI ';
    }

    protected function mkprowizja()
    {
        $mgo = Mymongo::activate();
        $oferty = $mgo->mget_active_offers_list();
        $mgo->normalizeReadDates($oferty);
        $oferty = Userproducts::UnsetUnavailableOffers($oferty);
        $prowizje = $this->en->select('select * from ' . TABLE_PREFIX . 'prowizja_aktualna WHERE current=1');
        foreach ($prowizje as $v) {
            $prow[$v['id_produktu']] = $v;
        }
        $c = 0;
        unset($prowizje);
        foreach ($oferty as $offer) {
            Tools::FindProvision($offer['offer'], $prow);
            $c++;
        }
        echo 'done...0PR  ' . $c;
    }

    protected function mkpricelist()
    {
        $mgo = Mymongo::activate();
        $oferty = $mgo->mget_active_offers_list();
        $mgo->normalizeReadDates($oferty);
        $oferty = Userproducts::UnsetUnavailableOffers($oferty);
        $this->en->query('truncate ' . TABLE_PREFIX . 'cennik');
        $c = 0;
        foreach ($oferty as $offer) {
            Tools::PreparePricelists($offer['offer']);
            $c++;
        }
        echo 'done...PRC' . $c;
    }

    public function LogInsert($rodzaj, $rid, $opis)
    {
        $res = $this->en->query(sprintf("INSERT INTO `" . TABLE_PREFIX . "log`(`data`,`rodzaj`,`rid`,`opis`,`user`) VALUES('%s','%s',%d,'%s',%d)",
            date("Y-m-d H:i:s"), $rodzaj, $rid, $opis, intval(User::GetUserID())
        ));
    }
//
//    public function SendErrorInfo($subject, $body) {
//
//        self::SendMessage($subject, $body, $params['email'], $sender, MAILER_NAME, MAILER, unserialize(MAILER_CONF), $files);
//    }

    public function MkRozliczeniePDF($id)
    {
        echo "Rozliczenie nr $id <br>";
        $r = $this->en->select_r('SELECT * FROM ' . TABLE_PREFIX . 'rozliczenia WHERE id=' . $id);
        $mgo = Mymongo::activate();
        $rid = new MongoId($r['mgoid']);
        $offer = $mgo->db->rozliczenia->findOne(array('_id' => $rid));
        $offer = $mgo->Mgo2array($offer);
        if (false === isset($offer['_id'])) {
            echo 'bad rozid';
            return;
        }
        echo "Operacje: " . count($offer['operacje']) . "<br>";
        echo "Operacje WP: " . count($offer['wp_operacje']) . "<br>";
        echo "Zwroty: " . count($offer['zwroty_operacje']) . "<br>";
        echo "PWID: " . PAYWALLET_ID . "<br>";
        $digitalsig = $this->en->select_r('SELECT * FROM ' . TABLE_PREFIX . 'digitalsig WHERE event_id = "' . $r['mgoid'] . '"');
        $authcode = $digitalsig['authcode'];
        $comment = 'Automatyczna akceptacja oferty';
        $sigpath = '';
        switch ($offer['ptype']) {
            case 2:
                require 'application/admin_v4_0/modules/finance/PDFOsrodek.php';
                break;
            case 1:
            case 6:
            case 3:
            case 9:
                require 'application/admin_v4_0/modules/finance/PDFSprzedaz.php';
                break;
        }
//        echo $pdfpath . '<br>';
        echo 'OK';
    }


}

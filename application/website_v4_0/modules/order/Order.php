<?php

use App\Models\KuponTemp;
use App\Models\KuponyEmisje;
use App\Services\KartyGrupyService;
// error_reporting(E_ALL);

include_once(ENSETTINGSDIR . 'Decorator.Smarty.php');

class Order extends Module
{

    public $app = null;
    protected $sdb = null;
    protected $post = null;
    protected $get = null;
    protected $alerts = [];
    protected $tpl_data = [];
    protected $tpl_conf = [];
    protected $ajax_data = null;
    protected $ajax_resp = null;
    protected $widget_header_data = [];
    protected $widget_basket_data = [];
    protected ?BasketData $basket = null;
    protected ?OfferData $offer = null;
    protected $order = null;
    protected $lang = null;
    protected $currency = null;
    protected $up = null; // UserProductsData
    public $meta = null; // Offer metadata
    public $mgo = null; // Offer metadata
    public $currentOffer = null;
    public string $error = '';
    protected bool $ticket_insurance_present = false;

    public function __construct($app)
    {
        $this->app  = &$app;
        $this->sdb  = Database::activate();
        $this->get  = $this->app->urlparam;
        $this->post = $this->app->postparam;
        $this->mgo = Mymongo::activate();
        $this->getOfferMeta();
        $this->app->setTranslations($this->lang);
        $this->app->SetLang($this->lang);

        switch(strtolower($this->get['a']))
        {
            default                    : $this->main();                    break;
            case 'configure'           : $this->configure();               break;
            case 'addtobasket':
            case 'ajaxaddtobasket'     : $this->ajaxAddToBasket();         break;
            case 'ajaxconfpayment'     : $this->ajaxConfPayment();         break;
            case 'addtobaskethtml':
            case 'ajaxaddtobaskethtml' : $this->ajaxAddToBasketHtml();     break;
            case 'ajaxgetbasketcont'   : $this->ajaxGetBasketCont();       break;
            case 'ajaxcheckcardnumber' : $this->ajaxCheckCardNumber();     break;
            case 'ajaxdelfrombasket'   : $this->ajaxDelFromBasket();       break;
            case 'payment'             : $this->payment();                 break;
            case 'getcouponlist'       : $this->GetCouponListV2();       break;
            case 'ajaxfinishpayment'   : $this->ajaxFinishPayment();   break;
            case 'afterpay'            : $this->afterPay();            break;
            case 'setcoupon'           : $this->setCoupon();           break;
            case 'ajaxgettemplate'     : $this->ajaxGetTemplate();     break;
            case 'removecoupon'        : $this->removeCoupon();        break;
            case 'repeatpayment'       : $this->repeatPayment();       break;
            case 'ajaxrepeatpayment'   : $this->ajaxRepeatPayment();   break;
            case 'ajaxcoupontransport' : $this->ajaxcoupontransport(); break;
            case 'ajaxchangetransport' : $this->ajaxchangetransport(); break;
            case 'ajaxdeltranscoupon'  : $this->ajaxdeltranscoupon();  break;
            case 'confirmemail' : $this->confirmEmail(); break;
            case 'removepromo' : $this->removePromo(); break;
            case 'getcalendardata': $this->getCalendarData(); break;
            case 'ajaxgetupdatedbasket'     : $this->ajaxGetUpdatedBasket();         break;
            case 'setreturntoshopflag'     : $this->setReturnToShopFlag();     break;
            case 'resetreturntoshopflag'   : $this->resetReturnToShopFlagAjax(); break;
            case 'generatedynamicconfigurl' : $this->generateDynamicConfigUrl(); break;
            case 'generateconfigurestepurlsajax' : 
                $this->handleGenerateConfigureStepUrlsAjax(); 
                break;
            case 'generateconfigurestepbacklink' : $this->handleGenerateConfigureStepBackUrlsAjax(); break;

            // Funkcje testowe
            case 'paymentdata':
                $this->showDataOnScreen($_SESSION['payment'], 'PaymentData');
                exit();

            case 'basketdata':
                $this->showDataOnScreen($_SESSION['basket'], 'BasketData');
                exit();

            case 'removebasketdata':
                // CUSTOM COMMENTED OUT TO ALLOW ALL USERS TO REMOVE THE BASKET
                // if (false === User::GetUserID()) {
                //     abort(404);
                // }
                unset($_SESSION['basket']);
                $this->showDataOnScreen(['action' => 'Removed basketData'], 'RemoveBasketData');
                exit();


            case 'removepaymentdata':
                if (false === User::GetUserID()) {
                    abort(404);
                }
                unset($_SESSION['payment']);
                $this->showDataOnScreen(['action' => 'Removed PaymentData'], 'RemovePaymentData');
                exit();


            case 'sessiondata':
                $user = User::GetUserID();
                if (false === $user) {
                    abort(404);
                }

                if(false === User::amISuperAdmin()) {
                    abort(404);
                }

                $this->showDataOnScreen($this->app->settings, 'SessionData');
                exit();


            case 'session':
                $user = User::GetUserID();
                if (false === $user) {
                    abort(404);
                }

                if(false === User::amISuperAdmin()) {
                    abort(404);
                }

                $this->showDataOnScreen($_SESSION, 'SessionData');
                exit();
        }
    }

    protected function checkPromo()
    {
        if (!empty($_GET['promo']) && $_SERVER['REQUEST_METHOD'] === 'GET') {
            $serial = $_GET['promo'];
            $ks = new Kupon();

            /**
             * @var null|KuponTemp $kupon
             * @var null|KuponyEmisje $emission
             */
            [$kupon, $emission] = $ks->CheckPromo($serial);

            if (null === $kupon || null === $emission) {
                redirect('/order');
                exit();
            }

            $lang = $emission->getLangs()[LANG];
            $this->basket = new BasketData();
            $this->basket->setShopParentId($this->get['parentid'] ?? null);
            $this->basket->getBasket();
            $_SESSION['basket']['promocode'] = [
                'code' => $_GET['promo'],
                'temp_id' => $kupon->id,
                'used' => false,
                'emission' => $emission->asArray() + ['lang' => $lang]
            ];
            $kupon->reserveCoupon($emission->nrumowy, $emission);
            redirect('/order');
            exit();
        }

        if ($_SESSION['basket']['promocode'] && $_SESSION['basket']['promocode']['used'] === true) {
            $this->basket = new BasketData();
            $this->basket->setShopParentId($this->get['parentid'] ?? null);
            $this->basket->getBasket();
            Kupon::RemovePromoCoupons($this->basket);
            (new Kupon())->reservePromoCoupon($this->basket->basket['promocode']['temp_id'], $this->basket->basket['promocode']['emission']['nrumowy']);
        }

    }

    protected function removePromo()
    {
        $basket = new BasketData();
        $basket->getBasket();
        Kupon::removePromoFromBasket($basket);
        $basket->setBasket(false);
        $response['status'] = true;
        $this->app->SetDecorator('Ajax');
        $this->app->ADD('response', json_encode($response));
        return;
    }

    protected function showDataOnScreen($data, $title)
    {
        echo '<!DOCTYPE HTML>';
        echo '<html>';
        echo '<head>';
        echo '<title>' . $title. '</title>';
        echo '</head>';
        echo '<body>';
        Tools::PA($data, $title);
        echo '</body>';
        echo '</html>';
    }

    public function confirmEmail()
    {
        $this->ajax_data = $this->post['data'];
        if (empty($this->ajax_data['nonce']) || ($_SESSION['nonce'] ?? '') !== $this->ajax_data['nonce']) {
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Session expired! Please refresh']);
            die();
        }

        if (false === $this->checkNoLoginData()) {
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => implode(", ", $this->tpl_data['alerts'])]);
            die();
        }

        return $this->ajaxConfPayment();
    }

    public function getOfferMeta()
    {
        $now = new DateTime();
        $where = array(
            'status.id'       => 5,
            'current'         => 1,
            'partnerzy_sp.id' => INSTALLATION,
            'data_od' => [ '$lte' => new MongoDB\BSON\UTCDateTime($now)],
            'data_do' => [ '$gte' => new MongoDB\BSON\UTCDateTime($now)],
        );

        $cursor = $this->mgo->db->offers->findOne($where);
        $currentoffer = $this->mgo->Mgo2array($cursor);
        $this->lang = $currentoffer['lang'] ?: $_SESSION['lang'];
        if(!defined('LANG')){
            define('LANG', $this->lang);
        }
        $this->currency = $currentoffer['currency'] ?: $_SESSION['CURRENCY'];
        $this->app->SetCurrency($this->currency, true);
        $this->meta = $currentoffer['meta'];
        $this->widget_header_data['offerid'] = $currentoffer['parentid'];
        $this->currentOffer = $currentoffer;
    }

    public function main()
    {
        $this->checkPromo();
        $this->basket = new BasketData();
        $this->basket->setShopParentId($this->get['parentid']);
        $this->basket->getBasket();
        $this->basket->cleanPaymentSessionStep();
        $this->offer = new OfferData();
        $this->offer->setParentId($this->get['parentid']);
        $this->offer->setOperators('main_offer');
        $this->offer->getOffer();
        $this->offer->addProductDates('Y-m-d H:i');
        $this->offer->addProductPrice();

        $this->offer->addProductMinPrice();
        $this->offer->setProductAvailable();

        $this->offer->setProductResortsString();
        $this->offer->setSingleCardsOnly();
        $this->offer->setInsurancesStandAloneOnly();
        $this->offer->moveProductsUp();
        $this->offer->format();
                /* dd($this->offer->output); */

        
        $this->offer->formatOutput('group_insurances');
        $this->offer->formatOutput('group_coupons');
        $this->offer->formatOutput('sort_products');
        if (DEBUG) {
            Tools::PA($this->offer->output, false, 'file');
        }

        if (!$this->offer->result) {
            $this->alerts['warning'][] = $this->app->Lang('Podczas pobierania oferty wystąpił błąd', 'F_Zamówienie');
        }
        if (!$this->offer->output) {
            $this->alerts['info'][] = $this->app->Lang('Brak produktów w ofercie', 'F_Zamówienie');
        } else {
            // basket data for shop.tpl
            $this->basket->getWidgetData();
            $params['widget'] = $this->basket->widget;
            $params['current_tpl'] = 'shop';
            $params['basket'] = $this->basket->basket;
            $this->offer->addCalendar();

            // Add the return_to_shop flag to params
            $params['return_to_shop'] = isset($_SESSION['return_to_shop']) && $_SESSION['return_to_shop'] === true;

            $this->tpl_data['offers'] = $this->offer->output;

            // $this->addMockCoupons();
            $this->tpl_data['params'] = $params;

        }
       
        if ($this->basket->basket['promocode'] && $this->basket->basket['promocode']['used'] === false) {
            $this->tpl_data['promocode'] = $this->basket->basket['promocode'];
        }

        // unset($_SESSION['return_to_shop']);

        $this->display('shop');
    }


    private function addMockCoupons()
    {
        $mockCoupons = [
            [
                'parentid' => 'parent_1',
                'id_emisji' => 'emission_1',
                'identyfikator' => 'COUPON1',
                'rodzaj_kuponu' => 'USER-PREPAID',
                'id_product' => '2Q1',
                'nazwa' => 'Summer Ski Pass',
                'note1' => 'Valid for all summer ski resorts',
                'note2' => 'Cannot be combined with other offers',
                'item_counter' => 50
            ],
            [
                'parentid' => 'parent_2',
                'id_emisji' => 'emission_2',
                'identyfikator' => 'COUPON2',
                'rodzaj_kuponu' => 'USER-PREPAID',
                'id_product' => '2Q2',
                'nazwa' => 'Winter Ski Pass',
                'note1' => 'Valid for all winter ski resorts',
                'note2' => 'Limited time offer',
                'item_counter' => 30
            ],
            [
                'parentid' => 'parent_3',
                'id_emisji' => 'emission_3',
                'identyfikator' => 'COUPON3',
                'rodzaj_kuponu' => 'USER-PREPAID',
                'id_product' => '2Q3',
                'nazwa' => 'All-Season Pass',
                'note1' => 'Valid for all ski resorts year-round',
                'note2' => 'Best value for frequent skiers',
                'item_counter' => 20
            ]
        ];

        // Add mock coupons to the offer output
    }
    // Dodaj tę nową metodę do klasy Order
    private function getMockUserCards()
    {
        return [
            [
                'id' => 1,
                'serial' => 'CARD001',
                'img_path' => '/assets/images/cards/card1.jpg',
                'opis_karty' => 'Karta letnia'
            ],
            [
                'id' => 2,
                'serial' => 'CARD002',
                'img_path' => '/assets/images/cards/card2.jpg',
                'opis_karty' => 'Karta zimowa'
            ],
            [
                'id' => 3,
                'serial' => 'CARD003',
                'img_path' => '/assets/images/cards/card3.jpg',
                'opis_karty' => ''
            ],
            [
                'id' => 4,
                'serial' => 'CARD004',
                'img_path' => '/assets/images/cards/card4.jpg',
                'opis_karty' => 'Karta całoroczna'
            ],
        ];
    }

    private function calculateMockedDiscount()
    {
     // Modify the array with mock calendarData
           foreach ($this->offer->output['karnety'] as &$ticket) {
            // Randomly decide whether to add calendarData
            if (rand(0, 1)) { 
                $ticket['calendarData'] = [
                    'availableDates' => []
                ];
                // Add 10 random dates
                for ($i = 0; $i < 10; $i++) {
                    $randomDate = date('Y-m-d', strtotime('+'.rand(1, 30).' days'));
                    $discount = rand(2, 15) * 5; 
                    $ticket['calendarData']['availableDates'][$randomDate] = $discount.'%';
                }
            } else {
                // Some tickets without available dates
                $ticket['calendarData'] = [
                    'availableDates' => []
                ];
            }
            // Calculate discountMaxPrice and discountPriceRange
            $basePrice = 0; // Initialize base price

            if (count($ticket['calendarData']['availableDates']) > 0) {
                
           
            foreach ($ticket['wariant_cenowy'] as $variant) {
                if ($variant['nazwa'] == 'Normalny') {
                    $basePrice = $variant['cena_base'];
                    break; // Found the "Normalny" variant, no need to continue
                }
            }

            if ($basePrice > 0) {
                $maxDiscountPercent = 0;
                foreach ($ticket['calendarData']['availableDates'] as $date => $discountString) {
                    $discountPercent = (int)str_replace('% discount', '', $discountString);
                    $maxDiscountPercent = max($maxDiscountPercent, $discountPercent);
                }

                $discountMaxPrice = $basePrice - ($basePrice * $maxDiscountPercent / 100);
                $ticket['calendarData']['discountMaxPrice'] = number_format($discountMaxPrice, 2);
                $ticket['discountPriceRange'] = number_format($discountMaxPrice, 2) . ' - ' . $basePrice ;
                $ticket['maxDiscountPercent'] = $maxDiscountPercent; 


                $ticket['wariant_cenowy_discounts'] = [];
                foreach ($ticket['wariant_cenowy'] as $variant) {
                    $maxAppliedDiscount = $variant['cena_base'] - ($variant['cena_base'] * $maxDiscountPercent / 100);
                    // Round to nearest .00, .25, .50, or .75
                    $maxAppliedDiscount = round($maxAppliedDiscount * 4) / 4;
                    $ticket['wariant_cenowy_discounts'][$variant['bsid']] = $maxAppliedDiscount;
                }
            }
        }
        }


    }

    public function findInsuranceOfBasketTickets($orders)
    {
        $insu = false;
        $ub_exists = false;
        $addons = false;
        $insuranceToConfigure = [];
    
        foreach ($orders as $zamowienie) {
            $offer = $this->mgo->mget_offer_details($zamowienie['parentid']);
    
            if ($zamowienie['p_type'] === 'karnet') {
                foreach ($zamowienie['karnet'] as $karnet) {
                    if (!is_null($this->basket->id_product_to_change) && $karnet['id_product'] !== $this->basket->id_product_to_change) {
                        continue;
                    }
                    if ((bool) $karnet['mozliwe_ubezpieczenie'] === true) {
                        $insu = true;
                    }
                    if ((int) $karnet['has_addons'] === 1) {
                        $addons = true;
                    }
                }
            } elseif ($zamowienie['p_type'] == 'ubezpieczenie') {
                $insuranceToConfigure[] = $zamowienie;
            }
    
            if ($insu && isset($offer['ubezpieczenia'])) {
                foreach ($offer['ubezpieczenia'] as $ubezpieczenie) {
                    if ($ubezpieczenie['klient_indywidualny'] && $ubezpieczenie['dostepna_na_stronie']) {
                        $ub_exists = true;
                        break 2;
                    }
                }
            }
        }
    
        $insu = $ub_exists;
        // dd($insu);
        return $insu;
    }
    

    private function hasCardsToConfig($zamowienie)
    {
        
            if ($zamowienie['p_type'] == 'karta') {
               return true;
            }
        
        return false;
    }

    public function configure()
    {
        $this->basket = new BasketData();
        $this->basket->getBasket();
        $orders = $this->basket->basket['zamowienia'];
       
        $insuranceToConfigure = [];

        $offer = null;
       

        if (!empty($this->basket->basket['zamowienia'])) {

             // CHECK FOR UBIEZPIECZENIA OGOLNE 
            if(is_array($orders)) {

            
                foreach($orders as $order) {
                    if($order['p_type'] == 'ubezpieczenie') {
                    $insuranceToConfigure[] = $order;
                       
                    }
               
                }
            }
            // dd($insuranceToConfigure);

            
            $offer = $this->mgo->mget_offer_details($this->basket->basket['zamowienia'][0]['parentid']);
        }
        $this->basket->setPType($this->get['p_type']);
  
       



      
        if (!empty($this->get['p_step'])) {
            $this->basket->setPStep($this->get['p_step']);
        }

        if ($this->get['id_product']) {
            $this->basket->setIdProductToChange($this->get['id_product']);
        }
 
      
     
        if (!$this->basket->getProducts()) {
            $this->alerts['danger'][] = $this->app->Lang('Podczas pobierania danych wystąpił błąd.', 'F_Zamówienie');
            $this->main();
            return false;
        }
        
        if (!$this->basket->checkPStep()) {
            // Powrot do konfiguracji strzalką
         

         
          
               
                $this->alerts['danger'][] = $this->app->Lang('Podczas sprawdzania danych wystąpił błąd.', 'F_Zamówienie');
                $this->main();
                return false;
            

           
         
        }

    


        if (!$this->basket->getStepData()) {
            $this->alerts['danger'][] = $this->app->Lang('Podczas przygotowywana danych wystąpił błąd', 'F_Zamówienie');
            $this->main();
            return false;
        }
        // $this->tpl_conf['tpl_steps'] = $this->basket->getSteps($this->basket->p_type);
       
        $steps = $this->setPossibleSteps($offer, $this->basket->p_type, $this->get['id_product'] ?? null, $orders);
        // dd($steps);
     
        // dd($this->tpl_conf['tpl_steps']);




       
        $this->tpl_conf['isLastStep'] = $this->isLastStep($steps, $this->basket->p_step);
      
        
        $this->tpl_conf['previous_step'] = $this->generateBackLink($this->basket->p_type, $this->basket->p_step ?? 'none', $steps);

        // dd($this->tpl_conf['previous_step']);
        $this->tpl_conf['dir'] = $this->basket->p_type;
        $this->tpl_conf['tpl'] = $this->basket->p_step . 's';
     
        $this->tpl_data = $this->basket->output;


        // dd($this->basket->output);
        // GET ORDERS INSURANCE FLAG TO DISPLAY THE STEP OF INSU
        $insurance_present = $this->isTicketsWithInsu();
        $this->ticket_insurance_present = $insurance_present;
        $this->setAvailableTicketInsuranceSession();
   
       

         
                
        /*
         * voucher 1 karta 0 - zaznaczac
         * voucher 0 karta 1 cards > 0 - nie zaznaczac
         */
        foreach ($this->tpl_data['p_data'] as &$product) {
            $nosnik_total = 0;
            foreach ($product['nosnik'] ?? [] as $nosnik => $value) {
                if (($nosnik === 'voucher' && (int)$value === 1) || ($nosnik === 'karta' && (int)$value === 1)) {
                    $nosnik_total++;
                }
                if (($nosnik === 'karta' && (int)$value === 1 && !empty($product['cards']))) {
                    $nosnik_total += count($product['cards']);
                }
            }
            $product['nosnik_total'] = $nosnik_total;
        }
              // Now sort the entire array by bsid
        usort($this->tpl_data['p_data'], function($a, $b) {
            return $a['bsid'] <=> $b['bsid'];
        });

            // $this->tpl_data['usercards'] = $this->getMockUserCards();
        $this->tpl_data['p_data_json'] = json_encode($this->tpl_data['p_data'], JSON_HEX_QUOT | JSON_HEX_TAG);
        $this->tpl_data['available_insurance'] = $insurance_present;
       
        $this->display('configure');
    }

    public function setReturnToShopFlag()
    {
        ob_clean();
        try {
            $_SESSION['return_to_shop'] = true;
            $response = ['success' => true, 'return_to_shop' => true];
        } catch (Exception $e) {
            $response = ['success' => false, 'error' => $e->getMessage()];
        }
        header('Content-Type: application/json');
        echo json_encode($response);
        exit();
    }

    // Add this new method to reset the flag
    public function resetReturnToShopFlag()
    {
        $_SESSION['return_to_shop'] = false;
    }

    public function resetReturnToShopFlagAjax()
    {
        ob_clean();
        try {
            $this->resetReturnToShopFlag(); // Call the method to reset the flag
            $response = ['success' => true, 'return_to_shop' => false];
        } catch (Exception $e) {
            $response = ['success' => false, 'error' => $e->getMessage()];
        }
        header('Content-Type: application/json');
        echo json_encode($response);
        exit();
    }

    public function handleGenerateConfigureStepBackUrlsAjax()
    {
       
        $currentSteps = $this->post['current_steps'] ?? [];
        $steps = [];
        $baseUrl = 'order/configure'; // Base URL for configuration steps
            // Current step from frontend 
            // Contents String "ticks"
        $currentStepFromFront = $this->post['currentStep'] ?? null;
        $currentStepTruncated = '';

        // Remove last character if it is 's'
        if (is_string($currentStepFromFront) && substr($currentStepFromFront, -1) === 's') {
            $currentStepTruncated = substr($currentStepFromFront, 0, -1);
        } else {
            $currentStepTruncated = $currentStepFromFront;
        }

       
        // Step definitions with default parameters
        $stepDefinitions = [
            'calendar' => ['p_type' => 'karnet', 'p_step' => 'calendar'],
            'tick' => ['p_type' => 'karnet', 'p_step' => 'tick'],
            'opti' => ['p_type' => 'karnet', 'p_step' => 'opti'],
            'insu' => ['p_type' => 'karnet', 'p_step' => 'insu'],
            'insurances_only' => ['p_type' => 'ubezpieczenie', 'p_step' => 'type'],
            'card' => ['p_type' => 'karta', 'p_step' => 'card'],
        ];

        $stepBackUrl = 'order'; // Default back URL

        if (array_key_exists($currentStepTruncated, $stepDefinitions)) {
            $currentStepIndex = array_search($currentStepFromFront, $currentSteps);
            if(!$currentStepIndex) {
                $currentStepIndex = array_search($currentStepTruncated, $currentSteps);
            }
            // var_dump('current step index', $currentStepIndex);
            if ($currentStepIndex !== false && $currentStepIndex > 0) {
                // There is a previous step in currentSteps
                $previousStep = $currentSteps[$currentStepIndex - 1];
                // var_dump('previous step', $previousStep);
                $params = $stepDefinitions[$previousStep] ?? [];
                // var_dump('previous step in params', $params);
                if (!empty($params)) {
                    $stepBackUrl = $baseUrl . '?' . http_build_query($params);
                }
            }
          
        }


        $this->app->ADD('response', json_encode($stepBackUrl));
        $this->app->SetDecorator('Ajax');
    }

    public function handleGenerateConfigureStepUrlsAjax()
    {
     
        $currentSteps = $this->post['current_steps'] ?? []; 
       
        $steps = [];
        $baseUrl = 'order/configure'; // Base URL for the configuration steps
    
        // Define the steps and their corresponding parameters
        $stepDefinitions = [
            'calendar' => ['p_type' => 'karnet', 'p_step' => 'calendar'],
            'tick' => ['p_type' => 'karnet', 'p_step' => 'tick'],
            'opti' => ['p_type' => 'karnet', 'p_step' => 'opti'],
            'insu' => ['p_type' => 'karnet', 'p_step' => 'insu'],
            'insurances_only' => ['p_type' => 'ubezpieczenie', 'p_step' => 'type'],
            'card' => ['p_type' => 'karta', 'p_step' => 'card'],
            // Add more steps as needed
        ];
    
        // Iterate through each step definition
        foreach ($stepDefinitions as $step => $params) {
           
            // Check if the current step is in the provided current steps
            if (in_array($step, $currentSteps)) {
              
                $steps[$step] = [
                    'url' => $baseUrl . '?' . http_build_query($params),
                    'p_type' => $params['p_type'],
                    'p_step' => $params['p_step'],
                ];
            }
        }
    
        // Add the response to the app
        $this->app->ADD('response', json_encode($steps));
        $this->app->SetDecorator('Ajax');
    }


    public function generateDynamicConfigUrl()
    {
        ob_clean();
        $this->basket = new BasketData();
        $this->basket->getBasket();

        $url = 'order/configure';
        $params = [];
        $types = [];
    
        if (!empty($this->basket->basket['zamowienia'])) {
            // Iterate through all orders in the basket
            foreach ($this->basket->basket['zamowienia'] as $order) {
                $pType = $order['p_type'] ?? '';
                if (!in_array($pType, $types)) {
                    $types[] = $pType;
                }
            }
    
            // Set parameters based on the priority order
            if (in_array('karta', $types)) {
                $params['p_type'] = 'karta';
                $params['p_step'] = 'card';
            } elseif (in_array('ubezpieczenie', $types)) {
                $params['p_type'] = 'ubezpieczenie';
                $params['p_step'] = 'type';
            } elseif (in_array('karnet', $types)) {
                $params['p_type'] = 'karnet';
                $params['p_step'] = 'tick';
            } else {
                // Handle other product types if needed
                $params['p_type'] = '/order';
                // $params['p_step'] = ''; 
            }
        
        }
    
        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }
    
        $response = [
            'success' => true, 
            'url' => $url,
            'types' => $types  // Include all types found in the basket
        ];
        header('Content-Type: application/json');
        echo json_encode($response);
        exit();
    }
    
    // Get and set methods for available ticket insurance to track in 'Types' step
    private function setAvailableTicketInsuranceSession()
    {
        $_SESSION['basket']['available_insurance'] =  $this->ticket_insurance_present;
    }
    private function getAvailableTicketInsurance()
    {
        $this->ticket_insurance_present = $_SESSION['basket']['available_insurance'] ?? false;
        return $_SESSION['basket']['available_insurance'];
    }


    public function isLastStep(array $steps, string $current_step)
    {
        $start = false;
        $isLast = true;
        foreach ($steps as $step => $valid) {
            if ($step === $current_step) {
                $start = true;
            }
            if (!$start) {
                continue;
            }

            if ($valid && $step !== $current_step && $step !== 'all') {
                $isLast = false;
            }
            if (false === $isLast) {
                break;
            }
        }

        return $isLast;
    }


    public function getTicketsData($basket_id_product, $offer, $orders) 
    {
        $steps = $this->basket->getSteps('karnet');
        
        $values = array_fill(0, count($steps), true);         
        $steps = array_combine($steps, $values);
        
        $insu = false;
        $ub_exists = false;
        $addons = false;
        $tick = false;

        $insurances_only = false;

        foreach ($this->basket->basket['zamowienia'] as $zamowienie) {
            if ($zamowienie['p_type'] === 'karnet') {
                $tick = true;
                foreach ($zamowienie['karnet'] as $karnet) {
                    if (!is_null($basket_id_product) && $karnet['id_product'] !== $basket_id_product) {
                        continue;
                    }
                   
                    if ((int) $karnet['has_addons'] === 1) {
                        $addons = true;
                    }
                    
                }
                
            }

            // look for insurances in the basket
            if($zamowienie['p_type'] == 'ubezpieczenie') {
                $insurances_only = true;
                
            }
           
        }

       
       
        
        $insu = $this->findInsuranceOfBasketTickets($orders);   
   

        $steps['karnet_insu'] = $insu;
        $steps['opti'] = $addons;
        $steps['tick'] = $tick;
      
        $this->tpl_conf['insurances_only']= $insurances_only;

        $this->tpl_conf['tpl_steps']= $steps;
        return $steps;
    }

    public function getCardsData($basket_id_product, $offer, $orders)
    {
     

        $cards = false; 

        foreach ($this->basket->basket['zamowienia'] as $zamowienie) {
            if ($zamowienie['p_type'] === 'karta') {
                $cards = true; 
                break; 
            }
        }

        $steps['card'] = $cards; 
        return $steps; 
    }

    public function getInsuranceSteps($basket_id_product, $offer, $orders)
    {
    

        $insurances_only = false; 

        foreach ($this->basket->basket['zamowienia'] as $zamowienie) {
            if ($zamowienie['p_type'] === 'ubezpieczenie') {
                $insurances_only = true; 
              
                break; 
            }
        }

        $steps['insurances_only'] = $insurances_only; 
        return $steps;
    }


    public function setPossibleSteps($offer, $p_type, $basket_id_product = null, $orders = null)
    {

        // dd($orders);
        switch ($p_type) {
            case 'karnet':
                $steps = $this->basket->getSteps('karnet');
               
                $values = array_fill(0, count($steps), true);         
                $steps = array_combine($steps, $values);
                $insu = false;
                $ub_exists = false;
                $addons = false;
                $calendar = false;

                $insurances_only = false;

                $card_step = false;

                foreach ($this->basket->basket['zamowienia'] as $zamowienie) {
                    if ($zamowienie['p_type'] === 'karnet') {
                        foreach ($zamowienie['karnet'] as $karnet) {
                            if (!is_null($basket_id_product) && $karnet['id_product'] !== $basket_id_product) {
                                continue;
                            }
                            if ((bool) $karnet['mozliwe_ubezpieczenie'] === true) {
                                $insu = true;         
                            }
                            if ((int) $karnet['has_addons'] === 1) {
                                $addons = true;
                            }
                            
                        }
                        
                    }

                    // look for insurances in the basket
                    if($zamowienie['p_type'] == 'ubezpieczenie') {
                        $insurances_only = true;
                        
                    }
                       // Check for cards in the zamowienie
                    if ($this->hasCardsToConfig($zamowienie)) { 
                        $card_step = true; // Set card_step to true if cards are found
                    }
                   
                }
   
            //    dd($this->basket->basket['zamowienia']);
                if ($insu) {
                   
                    if ($offer['ubezpieczenia']) {
                        foreach ($offer['ubezpieczenia'] as $ubezpieczenie) {
                            if ($ubezpieczenie['klient_indywidualny'] && $ubezpieczenie['dostepna_na_stronie']) {
                                $ub_exists = true;
                                break;
                            }
                        }
                    }
                    $insu = $ub_exists;
                    if(!$insu && $orders) {

                        $insu = $this->findInsuranceOfBasketTickets($orders);
                    }
                   
                } else {
                    if($orders) {
                        $insu = $this->findInsuranceOfBasketTickets($orders);
                    }
                    
                }
              
                
                
                $calendar = true;


                $insurance_present = $this->isTicketsWithInsu();

                $this->ticket_insurance_present = $insurance_present;
                $this->setAvailableTicketInsuranceSession();

                $steps['insu'] = $insu;
                $steps['opti'] = $addons;
                // calendar steps data 
                // $steps['calendar'] = $calendar;
                // $steps['insurances_only'] = $insurances_only;
                $this->tpl_conf['insurances_only']= $insurances_only;
                $this->tpl_conf['card']= $card_step;
//                $steps['card'] = false;
                $this->tpl_conf['tpl_steps']= $steps;
                
                return $steps;

            case 'karta':
                $steps = $this->basket->getSteps('karta');
                $values = array_fill(0, count($steps), true);
                $steps = array_combine($steps, $values);
                $ticketSteps =  $this->getTicketsData($basket_id_product, $offer, $orders);
                $insuranceSteps =  $this->getInsuranceSteps($basket_id_product, $offer, $orders);
               
                $this->tpl_conf['previous_step'] = $this->generateBackLink($this->basket->p_type, $this->basket->p_step ?? 'none', $steps); 
                
                $this->tpl_conf['tpl_steps']= $steps + $ticketSteps + $insuranceSteps;
                return $steps;

            case 'ubezpieczenie':

                // dd($this->basket->basket['zamowienia']);
                $steps = $this->basket->getSteps('ubezpieczenie');
               $ticketSteps =  $this->getTicketsData($basket_id_product, $offer, $orders);
               $cardSteps =  $this->getCardsData($basket_id_product, $offer, $orders);
                $values = array_fill(0, count($steps), true);
                $steps = array_combine($steps, $values);
                $steps = $steps + $ticketSteps + $cardSteps;

                
                
                $this->tpl_conf['previous_step'] = $this->generateBackLink($this->basket->p_type, $this->basket->p_step ?? 'none', $steps); 
                
                $this->tpl_conf['tpl_steps']= $steps;
                return $steps;
        }
    }

    // Find tickets that have 'insu' prop and return bool
    private function isTicketsWithInsu():bool
    {
        $ordersWithInsu =$this->basket->output['p_data'];
               
        // GET ORDERS INSURANCE FLAG TO DISPLAY THE STEP OF INSU
        $insurance_present = false;
      
   
        if(is_array($ordersWithInsu)) {

            
            foreach($ordersWithInsu as $order) {
                if(!empty($order['insu'])) {
                 
                    $insurance_present = true;
                    break;
                }
            }
        }
        $this->ticket_insurance_present = $insurance_present;
        $this->setAvailableTicketInsuranceSession();
        return $insurance_present;
    }

    public function ajaxAddToBasket()
    {
        $this->ajax_data = $this->post['data'];

        error_log('ajaxAddToBasket data: ' . json_encode($this->ajax_data, JSON_PRETTY_PRINT));
        $this->ajax_resp = array(
            'ok' => false,
            'error' => false,
        );

        if (!$this->addNewOrder()) {
            $error_msg = $this->app->Lang('Nie udało się dodać produktu do koszyka.', 'F_Zamówienie');
            $this->ajax_resp['error'] = Tools::makeErrorsString($this->ajax_resp['error'], $error_msg);
        } else {
            $this->ajax_resp['ok'] = true;
        }

        $this->app->ADD('response', json_encode($this->ajax_resp));
        $this->app->SetDecorator('Ajax');
    }

 
    private function reorderInsuranceSteps($possible_steps)
    {
        $desiredOrder = ["tick", "opti", "karnet_insu", "type"]; // Define desired order

        $reorderedArray = [];

      
        foreach ($desiredOrder as $key) {
            if (array_key_exists($key, $possible_steps)) {
                $reorderedArray[$key] = $possible_steps[$key];
            }
        }

       
        foreach ($possible_steps as $key => $value) {
            if (!array_key_exists($key, $reorderedArray)) {
                $reorderedArray[$key] = $value;
            }
        }

        return $reorderedArray;
    }

     private function generateBackLink($ptype, $currentStep, $possible_steps)
    {
    
        switch ($ptype) {
            case "ubezpieczenie":
                
                $reorderedSteps = $this->reorderInsuranceSteps($possible_steps);
               

                $previous = '';
                foreach($possible_steps as $step=>$state) {
                    if($step === 'all' || $state === false) {
                        continue;
                    }
                    if ($currentStep === 'types') {
                        $currentStep = 'type';
                    }
                    if ($step === $currentStep) {
                        $previous == 'karnet_insu' && $previous = 'insu';
                       
                        return $previous;
                    }
                    
                    $previous = $step;
                }
               
                return 'unknown';

                break;

                default:
                $previous = '';
                // dd($possible_steps);
                foreach($possible_steps as $step=>$state) {
                    if($step === 'all' || $state === false) {
                        continue;
                    }
                    // ** Special handling for 'ticks' and 'types' remains the same **
                    if ($currentStep === 'ticks') {
                        $currentStep = 'tick';
                    } elseif ($currentStep === 'types') {
                        $currentStep = 'type';
                    } elseif ($currentStep === 'calendars') {
                        $currentStep = 'calendar';
                    }
                    // ** Return the previous step based on the new order **
                    if ($step === $currentStep) {
                        // dd($previous);
                        return $previous;
                    }
                    $previous = $step;
                }
                return 'unknown';
        }


           
    }

    private function addNewOrder()
    {
        $this->basket = new BasketData();
        $this->basket->getBasket();
        $this->basket->setPType($this->ajax_data['p_type']);
        $this->ajax_data['amount'] = (int)$this->ajax_data['amount'];

        if ($this->ajax_data['p_type'] !== 'ubezpieczenie') {
            $this->basket->setParentId($this->ajax_data['parentid']);
        } else {
            $this->basket->setPartnerId($this->ajax_data['partnerid']);
            $this->basket->setInsurer($this->ajax_data['insurer']);
        }

        if ($this->ajax_data['amount'] < 1) {
            $error_msg = $this->app->Lang('Podana ilość produktów jest nieprawidłowa.', 'F_Zamówienie');
            $this->ajax_resp['error'] = Tools::makeErrorsString($this->ajax_resp['error'], $error_msg);
            return false;
        }

        switch ($this->ajax_data['p_type']) {
            case 'karnet':
                $ticket['identyfikator'] = $this->ajax_data['identyfikator'];
                $ticket['bsid'] = $this->ajax_data['bsid'];

                for ($i = 0; $i < $this->ajax_data['amount']; $i++) {
                    $this->basket->setProductToAdd('order');
                    $this->basket->addToBasket();
                    $this->basket->setTicket($ticket);
                    $this->basket->addProductPrice();
                    $this->basket->addProductDates();
                    $this->basket->addProductHasPack();
                    $this->basket->addProductHasAddons();
                    $this->basket->setProductToAdd('ticket');
                    $this->basket->addToBasket();
                }
                break;

            case 'voucher':
                $voucher['identyfikator'] = $this->ajax_data['identyfikator'];
                $voucher['bsid'] = $this->ajax_data['bsid'];
                $selected_ids = $this->ajax_data['selected_attractions_ids'] ?? '';
                $voucher['selected_attractions_ids'] = !empty($selected_ids) 
                ? explode(',', $selected_ids) 
                : [];
                error_log('voucher selected_attractions_ids: ' . json_encode($selected_ids, JSON_PRETTY_PRINT));

                for ($i = 0; $i < $this->ajax_data['amount']; $i++) {
                    $this->basket->setProductToAdd('order');
                    $this->basket->addToBasket();
                    $this->basket->setVoucher($voucher);
                    $this->basket->addProductPrice();
                    $this->basket->addProductDates();
                    $this->basket->setProductToAdd('voucher');
                    $this->basket->addToBasket();
                }
                break;

            case 'produkt_wirtualny':
                $virtual_product['identyfikator'] = $this->ajax_data['identyfikator'];
                $virtual_product['source'] = 'standalone';

                for ($i = 0; $i < $this->ajax_data['amount']; $i++) {
                    $this->basket->setProductToAdd('order');
                    $this->basket->addToBasket();
                    $this->basket->setVirtualProduct($virtual_product);
                    $this->basket->addProductPrice();
                    $this->basket->addProductDates();
                    $this->basket->setProductToAdd('virtual_product');
                    $this->basket->addToBasket();
                }
                break;

            case 'karta':
                $card['identyfikator'] = $this->ajax_data['identyfikator'];
                $card['source'] = 'new';

                for ($i = 0; $i < $this->ajax_data['amount']; $i++) {
                    $this->basket->setProductToAdd('order');
                    $this->basket->addToBasket();
                    $this->basket->setCard($card);
                    $this->basket->addProductPrice();
                    $this->basket->addProductDates();
                    $this->basket->setProductToAdd('card');
                    $this->basket->addToBasket();
                }
                break;

            case 'ubezpieczenie':
                for ($i = 0; $i < $this->ajax_data['amount']; $i++) {
                    $this->basket->addInsuranceOrder();
                }
                break;

            case 'kupon':
                $coupon['parentid'] = $this->ajax_data['parentid'];
                $coupon['id_emisji'] = $this->ajax_data['id_emisji'];
                $coupon['kwota_brutto'] = $this->ajax_data['kwota'];
                $coupon['identyfikator'] = $this->ajax_data['identyfikator'];
                $coupon['rodzaj_kuponu'] = $this->ajax_data['rodzaj_kuponu'];
                $coupon['id_product'] = $this->ajax_data['id_product'];
                $coupon['nazwa'] = $this->ajax_data['nazwa'];

                for ($i = 0; $i < $this->ajax_data['amount']; $i++) {
                    $this->basket->setProductToAdd('order');
                    $this->basket->addToBasket();
                    $this->basket->setCoupon($coupon);
                    $this->basket->setProductToAdd('coupon');
                    $this->basket->addToBasket();
                }
                break;
        }
        return true;
    }

    public function ajaxAddToBasketHtml()
    {
        $this->ajax_data = $this->post['data'];
      
        if (!$this->confOrder()) {
            $error_msg = $this->app->Lang('Podczas operacji wystąpił błąd!', 'F_Zamówienie');
            $error_msg .= ' ' . $this->error;
            $this->ajax_resp['error'] = Tools::makeErrorsString($this->ajax_resp['error'], $error_msg);
        }
        $this->app->SetDecorator('Ajax');

        if ($this->ajax_resp['error']) {
            $this->app->ADD('response', json_encode($this->ajax_resp));
            return;
        }
       
        
        if ($this->tpl_conf['tpl'] !== 'all') {
            $this->tpl_conf['previous_step'] = $this->generateBackLink($this->basket->p_type, $this->basket->p_step, $this->tpl_conf['tpl_steps']);
         
            $this->tpl_conf['isLastStep'] = $this->isLastStep($this->tpl_conf['tpl_steps'], $this->basket->p_step);
            $this->tpl_conf['tpl'] .= 's';


                   // Now sort the entire array by bsid
        usort($this->ajax_resp['p_data'], function($a, $b) {
            return $a['bsid'] <=> $b['bsid'];
        });
        // dd($this->basket->output);


            $this->ajax_resp['p_data_json'] = json_encode($this->ajax_resp['p_data'], JSON_HEX_QUOT | JSON_HEX_TAG);
   


            // GET ORDERs INSURANCE FLAG TO DISPLAY THE STEP OF INSU
            $this->isTicketsWithInsu();
            $this->ajax_resp['available_insurance'] = $this->ticket_insurance_present;
            
                



            $this->app->AddDecoratorData('tpl_conf', $this->tpl_conf);
            $this->app->AddDecoratorData('data', $this->ajax_resp);
            
            $this->app->SetTemplate('modules/order/configure/' . $this->tpl_conf['dir'] . '/' . $this->tpl_conf['tpl'] . '.tpl');
            $resp = new DecoratorSmarty($this->app);
            echo $resp->output;
        }
    }

    private function confOrder()
    {
        $this->basket = new BasketData();
        $this->basket->getBasket();
        $orders = $this->basket->basket['zamowienia'];
     
    
        $this->basket->setPType($this->ajax_data['p_type']);

        if ($this->ajax_data['id_product_to_change']) {
            $this->basket->setIdProductToChange($this->ajax_data['id_product_to_change']);
        }
       
        $this->tpl_conf['dir'] = $this->ajax_data['p_type'];
        $f_type = ucfirst($this->ajax_data['p_type']);
        $f_step = ucfirst($this->ajax_data['p_step']);
        $f_name = 'conf' . $f_type . $f_step;

        if (!method_exists($this, $f_name)) {
            $this->error = 'method ' . $f_name . ' does not exists';
            return false;
        }
      
        
        if (!call_user_func(array($this, $f_name))) {
            $this->error = 'method ' . $f_name . ' is not callable';
            return false;
        }
       
    
        if (!$this->ajax_resp['error']) {
            
            if (!$this->basket->getProducts()) {
                $this->error = 'Get products ' . $f_name . ' problem';
                return false;
            }
         



            if (!$this->basket->getStepData()) {
                $this->error = 'GetStepData ' . $f_name . ' error. FN: ' . $this->basket->error;
                return false;
            }
            
            $this->tpl_conf['tpl_steps'] = $this->setPossibleSteps($offer, $this->basket->p_type, $this->basket->id_product_to_change, $orders);
           
           
            $this->ajax_resp = $this->basket->output;
            $this->tpl_conf['tpl'] = $this->basket->p_step;
        }

        return true;
    }

    private function confUbezpieczenieType()
    {
        if ($this->ajax_data['p_data']) {
            //  Sprawdzanie danych przed dodanie do koszyka
            foreach ($this->ajax_data['p_data'] as $p_data) {
                $l_uro = $this->app->Lang('data urodzenia w ubezpieczeniu', 'F_Zamówienie');
                $l_roz = $this->app->Lang('data rozpoczęcia w ubezpieczeniu', 'F_Zamówienie');
                $l_ema = $this->app->Lang('email w ubezpieczeniu', 'F_Zamówienie');
                $l_imi = $this->app->Lang('imię w ubezpieczeniu', 'F_Zamówienie');
                $l_naz = $this->app->Lang('nazwisko w ubezpieczeniu', 'F_Zamówienie');

                $val = array(
                    array($p_data['insurance']['data_ur'], 'REQUIRED', $l_uro . ' #' . $p_data['insu_num']),
                    array($p_data['insurance']['data_ur'], 'DATE', $l_uro . ' #' . $p_data['insu_num']),
                    array($p_data['insurance']['data_rozpoczecia'], 'REQUIRED', $l_roz . ' #' . $p_data['insu_num']),
                    array($p_data['insurance']['data_rozpoczecia'], 'DATE', $l_roz . ' #' . $p_data['insu_num']),
                    array($p_data['insurance']['email'], 'REQUIRED', $l_ema . ' #' . $p_data['insu_num']),
                    array($p_data['insurance']['email'], 'EMAIL', $l_ema . ' #' . $p_data['insu_num']),
                    array(array($p_data['insurance']['email'], 70), 'MAX_LENGTH', $l_ema . ' #' . $p_data['insu_num']),
                    array($p_data['insurance']['imie'], 'REQUIRED', $l_imi . ' #' . $p_data['insu_num']),
                    array($p_data['insurance']['imie'], 'ALPHA', $l_imi . ' #' . $p_data['insu_num']),
                    array(array($p_data['insurance']['imie'], 50), 'MAX_LENGTH', $l_imi . ' #' . $p_data['insu_num']),
                    array($p_data['insurance']['nazwisko'], 'REQUIRED', $l_naz . ' #' . $p_data['insu_num']),
                    array($p_data['insurance']['nazwisko'], 'SURNAME', $l_naz . ' #' . $p_data['insu_num']),
                    array(array($p_data['insurance']['nazwisko'], 70), 'MAX_LENGTH', $l_naz . ' #' . $p_data['insu_num']),
                );
                $val = Validation::ValForm($val);

                if ($val['errors']) {
                    foreach ($val['warnings'] as $warning) {
                        $this->ajax_resp['error'] = Tools::makeErrorsString($this->ajax_resp['error'], $warning);
                    }
                }
            }

            //  Dodanie do koszyka jeśli nie ma błędów
            if (!$this->ajax_data['error']) {
                foreach ($this->ajax_data['p_data'] as $p_data) {
                    $this->basket->setIdProduct($p_data['id_product']);
                    $this->basket->setIdBasket($p_data['id_basket']);
                    $this->basket->setInsurance($p_data['insurance']);
                    $this->basket->setProductToAdd('insurance');
                    $this->basket->addProductPrice();
                    $this->basket->addToBasket();
                }
            }
        }

        $this->basket->setPStep('all');
        return true;
    }

    private function confKartaCard()
    {
        if ($this->ajax_data['p_data']) {
            //  Sprawdzanie danych przed dodanie do koszyka
            foreach ($this->ajax_data['p_data'] as $p_data) {
                if ($p_data['card']['opis_karty']) {
                    $l_opi = $this->app->Lang('opis karty', 'F_Zamówienie');
                    $val = array(
                        array($p_data['card']['opis_karty'], 'ALPHA_NUMERIC_SPACE', $l_opi . ' #' . $p_data['card_num']),
                        array(array($p_data['card']['opis_karty'], 30), 'MAX_LENGTH', $l_opi . ' #' . $p_data['card_num'])
                    );
                    $val = Validation::ValForm($val);

                    if ($val['errors']) {
                        foreach ($val['warnings'] as $warning) {
                            $this->ajax_resp['error'] = Tools::makeErrorsString($this->ajax_resp['error'], $warning);
                        }
                    }
                }
            }

            //  Dodawanie do koszyka jeśli nie ma błędów
            if (!$this->ajax_resp['error']) {
                foreach ($this->ajax_data['p_data'] as $p_data) {
                    $this->basket->setIdProduct($p_data['id_product']);
                    $this->basket->setIdBasket($p_data['id_basket']);
                    $this->basket->setCard($p_data['card']);
                    $this->basket->addCardImgDesc();
                }
            }
        }

        $this->basket->setPStep('all');
        return true;
    }

    private function confKarnetTick()
    {
       
        if ($this->ajax_data['p_data']) {
            
            foreach ($this->ajax_data['p_data'] as $p_data) {
                $this->basket->setIdProduct($p_data['id_product']);
                $this->basket->setIdBasket($p_data['id_basket']);
                $this->basket->setPackMgoId($p_data['pack_identyfikator']);

                // SAVE INFO ABOUT SELECTED TICK NOSNIK 
                $this->basket->setSelectedCard($p_data['selected_card'] ?? null, $p_data['id_product']);

                $this->basket->addTicketPack();
                $this->basket->addProductPrice();
                $this->basket->addProductSource('zestaw');
                $this->basket->addPackVirtualProducts();
                $this->basket->deleteCards();

                if ($this->result) {
                    $this->basket->cleanTicketCard();
                }

                $this->basket->deleteInsurances();

                if ($this->result) {
                    $this->basket->cleanTicketInsurance();
                }
            }
        }

        if ($this->basket->checkIfAddonsAvailable()) {
            $this->basket->setNextPStep('tick');
        } else {
            $this->basket->setNextPStep('tick', 2);
        }
        return true;
    }

    private function confKarnetOpti()
    {
        if ($this->ajax_data['p_data']) {
            // dd($this->ajax_data['p_data']);
          
            foreach ($this->ajax_data['p_data'] as $p_data) {
                if (!$this->ajax_resp['error']) {
                    $this->basket->setIdProduct($p_data['id_product']);
                    $this->basket->setIdBasket($p_data['id_basket']);



$this->basket->setSelectedDodatki($p_data['options_info'] ?? [], $p_data['id_product']);

                    $this->basket->setProductToAdd('virtual_product');
//  var_dump($this->basket->basket);
                    foreach ($p_data['options'] as $option) {
                        $virtual_product['identyfikator'] = $option;
                        $this->basket->setVirtualProduct($virtual_product);
                        $this->basket->addProductPrice();
                        $this->basket->addProductSource('option');
                        $this->basket->addToBasket();
                    }
                //    dd($this->basket->basket);
                    $offer = $this->mgo->mget_offer_details($this->basket->basket['zamowienia'][0]['parentid']);
                    $this->tpl_conf['tpl_steps'] = $this->setPossibleSteps($offer, $this->basket->p_type, $this->basket->id_product_to_change); 
                 
                    // $this->setPossibleSteps($offer, $this->basket->p_type, $this->basket->id_product_to_change);
                }
            }
        }
        // dd($this->basket->basket['zamowienia'] );
        $this->basket->setNextPStep('opti');
        // modify tpl steps
        // $this->basket->setNextPStep('opti', 1, $this->tpl_conf['tpl_steps'] ?? []);
        return true;
    }

    private function confKarnetCard()
    {
        $this->basket = new BasketData();
        $this->basket->getBasket();
        $this->basket->setPType($this->ajax_data['p_type']);

        if($this->ajax_data['id_product_to_change'])
        {
            $this->basket->setIdProductToChange($this->ajax_data['id_product_to_change']);
        }

        if($this->ajax_data['p_data'])
        {
            //  Sprawdzanie danych przed dodanie do koszyka
            foreach($this->ajax_data['p_data'] as $p_data)
            {
                if($p_data['card']['opis_karty'])
                {
                    $l_opi = $this->app->Lang('opis karty do karnetu','F_Zamówienie');
                    $val = array(
                        array($p_data['card']['opis_karty'],            'ALPHA_NUMERIC_SPACE', $l_opi . ' #' . $p_data['tick_num']),
                        array(array($p_data['card']['opis_karty'], 30), 'MAX_LENGTH',          $l_opi . ' #' . $p_data['tick_num'])
                    );
                    $val = Validation::ValForm($val);

                    if($val['errors'])
                    {
                        foreach($val['warnings'] as $warning)
                        {
                            $this->ajax_resp['error'] = Tools::makeErrorsString($this->ajax_resp['error'], $warning);
                        }
                    }
                }
            }

            //  Dodawanie do koszyka jeśli nie ma błędów
            if(!$this->ajax_resp['error'])
            {
                foreach($this->ajax_data['p_data'] as $p_data)
                {
                    $this->basket->setIdProduct($p_data['id_product']);
                    $this->basket->setIdBasket($p_data['id_basket']);
                    $this->basket->setCard($p_data['card']);
                    $this->basket->deleteCards();

                    if($this->basket->result)
                    {
                        $this->basket->setProductToAdd('card');
                        $this->basket->addProductPrice();
                        $this->basket->addToBasket();
                        $this->basket->addTicketCard();
                    }
                }
            }
            $offer = $this->mgo->mget_offer_details($this->basket->basket['zamowienia'][0]['parentid']);
            $this->setPossibleSteps($offer, $this->basket->p_type, $this->basket->id_product_to_change);
        }
        $this->basket->setNextPStep('tick', 1, $this->tpl_conf['tpl_steps'] ?? []);
        return true;
    }
    private function confKarnetCalendar()
    {
    
       
        if ($this->ajax_data['p_data']) {
          
            foreach ($this->ajax_data['p_data'] as $p_data) {
                $this->basket->setIdProduct($p_data['id_product']);
                $this->basket->setIdBasket($p_data['id_basket']);

         
                $startDate = $p_data['startDate'] ?? null;
                $numberOfDays = $p_data['numberOfDays'] ?? null;
                $discount = $p_data['price'] ?? null; // Now represents the discount
                $endDate = "";
                
                if ($startDate && $numberOfDays && $discount !== null) {
                    // Calculate the end date
                    $endDate = date('Y-m-d', strtotime($startDate . ' + ' . ($numberOfDays - 1) . ' days'));
    
                     // Get the current price of the ticket
                    $currentPrice = $this->basket->getTicketPriceById($p_data['id_product']);

                  
                   
                    // Update the basket with the selected date,  and calculated end date
                    $this->basket->setTicketDate($startDate, $p_data['id_product']);
                    $this->basket->setTicketEndDate($endDate, $p_data['id_product']);

                    foreach ($this->basket->basket['zamowienia'] as &$order) {
                 
                            $order['p_conf']['set']['calendar'] = 1;
                            break; // No need to continue looping once the order is found
                     
                    }
               

                    $this->basket->setBasket();
                    // TODO: REPLACE WITH ACTUAL PRICE AND DATES 
                    
                } else {
                    // TODO: ERROR HANDLING THE CASE WHERE DATE OR DURATION IS NOT SELECTED
                    // $this->ajax_resp['error'] = Tools::makeErrorsString($this->ajax_resp['error'], 'Invalid date, duration, or discount selected');
                    // return false;
                }
            }
            
        }


        $offer = $this->mgo->mget_offer_details($this->basket->basket['zamowienia'][0]['parentid']);
        $this->tpl_conf['tpl_steps'] = $this->setPossibleSteps($offer, $this->basket->p_type, $this->basket->id_product_to_change); 
     

        // var_dump($this->basket['zamowienia']);
        // Set the next step in the configuration process
        $this->basket->setNextPStep('calendar'); 
      
        // $this->basket->setNextPStep('calendar', 1, $this->tpl_conf['tpl_steps'] ?? []);
        return true; 
    }
    private function confKarnetTicks()
    {
        if($this->ajax_data['p_data'])
        {
            // dd($this->ajax_data['p_data']);
            foreach($this->ajax_data['p_data'] as $p_data)
            {
                $this->basket->setIdProduct($p_data['id_product']);
                $this->basket->setIdBasket($p_data['id_basket']);
                $this->basket->setPackMgoId($p_data['pack_identyfikator']);

                
                    // SAVE INFO ABOUT SELECTED TICK NOSNIK 
                    $this->basket->setSelectedCard($p_data['card'] ?? null, $p_data['id_product']);

                $this->basket->addTicketPack();
                $this->basket->addProductPrice();
                $this->basket->addProductSource('zestaw');
                $this->basket->addPackVirtualProducts();
                $this->basket->deleteCards();

                if($this->result)
                {
                    $this->basket->cleanTicketCard();
                }

                $this->basket->deleteInsurances();

                if($this->result)
                {
                    $this->basket->cleanTicketInsurance();
                }
            }
        }
    
        $this->confKarnetCard();
        
        return true;
    }

    private function confKarnetInsu()
    {
        if($this->ajax_data['p_data'])
        {
            //  Sprawdzanie danych przed dodanie do koszyka
            foreach($this->ajax_data['p_data'] as $p_data)
            {
                if($p_data['insurance']['identyfikator'])
                {
                    $l_uro = $this->app->Lang('data urodzenia w ubezpieczeniu do karnetu','F_Zamówienie');
                    $l_roz = $this->app->Lang('data rozpoczęcia w ubezpieczeniu do karnetu','F_Zamówienie');
                    $l_ema = $this->app->Lang('email w ubezpieczeniu do karnetu','F_Zamówienie');
                    $l_imi = $this->app->Lang('imię w ubezpieczeniu do karnetu','F_Zamówienie');
                    $l_naz = $this->app->Lang('nazwisko w ubezpieczeniu do karnetu','F_Zamówienie');
                    $val = array(
                        array($p_data['insurance']['data_ur'],             'REQUIRED',   $l_uro . ' #' . $p_data['tick_num']),
                        array($p_data['insurance']['data_ur'],             'DATE',       $l_uro . ' #' . $p_data['tick_num']),
                        array($p_data['insurance']['email'],               'REQUIRED',   $l_ema . ' #' . $p_data['tick_num']),
                        array($p_data['insurance']['email'],               'EMAIL',      $l_ema . ' #' . $p_data['tick_num']),
                        array(array($p_data['insurance']['email'], 70),    'MAX_LENGTH', $l_ema . ' #' . $p_data['tick_num']),
                        array($p_data['insurance']['imie'],                'REQUIRED',   $l_imi . ' #' . $p_data['tick_num']),
                        array($p_data['insurance']['imie'],                'ALPHA',      $l_imi . ' #' . $p_data['tick_num']),
                        array(array($p_data['insurance']['imie'], 50),     'MAX_LENGTH', $l_imi . ' #' . $p_data['tick_num']),
                        array($p_data['insurance']['nazwisko'],            'REQUIRED',   $l_naz . ' #' . $p_data['tick_num']),
                        array($p_data['insurance']['nazwisko'],            'SURNAME',    $l_naz . ' #' . $p_data['tick_num']),
                        array(array($p_data['insurance']['nazwisko'], 70), 'MAX_LENGTH', $l_naz . ' #' . $p_data['tick_num']),
                    );
                    $val = Validation::ValForm($val);

                    if($val['errors'])
                    {
                        foreach($val['warnings'] as $warning)
                        {
                            $this->ajax_resp['error'] = Tools::makeErrorsString($this->ajax_resp['error'], $warning);
                        }
                    }
                }
            }

            //  Dodawanie do koszyka jeśli nie ma błędów
            if(!$this->ajax_resp['error'])
            {
                foreach($this->ajax_data['p_data'] as $p_data)
                {
                    $this->basket->setIdProduct($p_data['id_product']);
                    $this->basket->setIdBasket($p_data['id_basket']);
                    $this->basket->setInsurance($p_data['insurance']);
                    $this->basket->deleteInsurances();
                    $this->basket->setProductToAdd('insurance');
                    $this->basket->addProductPrice();
                    $this->basket->addToBasket();
                    $this->basket->addTicketInsurance();
                    $this->basket->setNextPStep('insu', 1, $this->tpl_conf['tpl_steps'] ?? []);
                }
            }
        }
        return true;
    }

    private function confKarnetZwgw()
    {
        if($this->ajax_data['p_data'])
        {
            foreach($this->ajax_data['p_data'] as $p_data)
            {
                $this->basket->setIdProduct($p_data['id_product']);
                $this->basket->setIdBasket($p_data['id_basket']);
                $this->basket->setReturn($p_data['zwgw']);
                $this->basket->addTicketReturn();
            }
        }

        $this->basket->setPStep('all');
        return true;
    }

    public function ajaxDelFromBasket()
    {
        if ($this->post['id_basket']) {
            $this->basket = new BasketData();
            $this->basket->getBasket();
            $this->basket->setIdBasket($this->post['id_basket']);
            $this->basket->deleteOrder();

            if ($this->basket->result) {
                $response['ok'] = true;
            } else {
                $response['error'] = $this->app->Lang('Nie udało się usunąć produktu z koszyka', 'F_Zamówienie');
            }
        } else {
            $response['error'] = $this->app->Lang('Brak danych do usunięcia produktu z koszyka', 'F_Zamówienie');
        }

        $this->app->ADD('response', json_encode($response));
        $this->app->SetDecorator('Ajax');
    }

    public function ajaxCheckCardNumber()
    {
        $this->app->SetDecorator('Ajax');
        $response = [];
        if (empty($this->post['serial'])) {
            $this->app->ADD('response', json_encode(['error' => $this->app->Lang('Podczas operacji wystąpił błąd','F_Zamówienie')]));
            return;
        }

        $serial = ltrim($this->post['serial'], '0');

        if($this->post['group'] > 0) {
            $res = KartyGrupyService::canIUseCard((int) $this->post['group'], $serial);
            if (null === $res) {
                $this->app->ADD('response', json_encode(['error' => $this->app->Lang('Ta  karta nie może być użyta do tego produktu','F_Zamówienie')]));
                return;
            }
            $response['data'] = KartyGrupyService::GetCheckCardResponse($res);
            $this->app->ADD('response', json_encode($response));
            return;
        }


        if (USER::LoggedIn()) {
            $check = Karta::ifCardExists($serial, USER::GetUserID());
        } else {
            $check = Karta::ifCardExists($serial, false, false);
        }

        if (empty($check['cid'])) {
            $this->app->ADD('response', json_encode(['error' =>
                $check['error'] ?: $this->app->Lang('Nie odnaleziono karty','F_Zamówienie')]));
            return;
        }

        $response['data'] = ['id' => $check['data']['id'], 'serial' => $check['data']['serial'], 'img_path' => $check['data']['img_path']];
        if (!empty($check['opis_karty'])) {
            $response['data']['opis_karty'] = $check['opis_karty'];
        }
        $response['data']['source'] = $check['source'] === 'main' ? 'base' : 'basetm';

        $this->app->ADD('response', json_encode($response));
    }

    /**
     * Płatność
     * @return boolean
     */
    public function payment()
    {
        
        $this->basket = new BasketData();
        $this->basket->getBasket();

        if (!$this->basket->checkBasket()) {
            $this->alerts['warning'][] = $this->basket->errors;
        } else {

            Kupon::AttachPromoCoupon($this->basket);
            $this->basket->AddToAllKuponAttachMissing();
            $this->basket->setBasket(false);
            $this->basket->saveOrder();

            $this->getPaymentStepData();
        }

        if ($this->alerts) {
            $this->main();
            return false;
        }
      

        $this->display('payment');
    }

    /**
     * Aktualizuje konfigurację płatności i zwraca widok kolejnego kroku
     */
    public function ajaxConfPayment()
    {
        $this->ajax_data = $this->post['data'];

//        if(!floatval(User::GetUserID()) > 0)
//        {
//            $button_text = $this->app->Lang('Zaloguj się','F_Zamówienie');
//            $error = '<span class="block">' . $this->app->Lang('Wykryto wylogowanie z systemu. Aby dokończyć płatność należy się ponownie zalogować.', 'F_Zamówienie') . '</span>';
//            $error.= '<a class="btn btn-info btn-sm margtop10" href="' . SITE_URL . 'userlogin/loginpage">' . $button_text . '</a>';
//            $this->alerts['info'][] = $error;
//            $this->app->AddWidgetsData('alerts', $this->alerts);
//            $this->tpl_conf['tpl'] = 'payment-errors';
//        }
//        else if ( !$this->addPaymentData() )
//        {
//            $this->alerts['warning'][] = $this->basket->errors;
//            $this->app->AddWidgetsData('alerts', $this->alerts);
//            $this->tpl_conf['tpl'] = 'payment-errors';
//        }

        if ( !$this->addPaymentData() )
        {
            $this->alerts['warning'][] = $this->basket->errors;
            $this->app->AddWidgetsData('alerts', $this->alerts);
            $this->tpl_conf['tpl'] = 'payment-errors';
        }

        $this->app->SetDecorator('Ajax');
        $this->app->AddDecoratorData('data', $this->tpl_data);
        $this->app->AddDecoratorData('tpl_conf', $this->tpl_conf);
        $this->app->SetTemplate('modules/order/payment/' . $this->tpl_conf['tpl'] . '.tpl');
        $resp = new DecoratorSmarty($this->app);
        echo $resp->output;
    }

    public function ajaxGetTemplate()
    {
        $this->basket = new BasketData;
        $this->basket->getBasket();
        $this->basket->getPayment();
        $this->basket->setPaymentStep('combined');
        $this->basket->setPayment();
        $this->getPaymentStepData();
        $this->app->SetDecorator('Ajax');
        $this->app->AddDecoratorData('data', $this->tpl_data);
        $this->app->SetTemplate('modules/order/payment/' . $this->tpl_conf['tpl'] . '.tpl');
        $resp = new DecoratorSmarty($this->app);
        echo $resp->output;
    }

    /**
     * Dodaje informacje do konfiguracji płatności
     * @return bool
     */
    public function addPaymentData()
    {
        $this->basket = new BasketData;
        $this->basket->getBasket();
        $this->basket->getPayment();
        $this->basket->setPaymentStep($this->ajax_data['next_step']);
        $this->basket->setPayment();

        switch($this->ajax_data['step'])
        {

            case 'path':
                if ($this->ajax_data['path'] === 'no-login') {
                    if (false === $this->checkNoLoginData()) {
                        return $this->getPaymentStepData();
                    }
                    $this->basket->basket['order_path'] = 'no-login';
                    $this->basket->basket['user_email'] = $this->ajax_data['user']['email'];
                    $this->basket->setBasket(false);
                    $this->basket->saveOrder();
                }
                break;

            case 'basket':
//                if ($this->ajax_data['path'] == 'no-login') {
//                    if (false === $this->checkNoLoginData()) {
//                        return $this->getPaymentStepData();
//                    }
//                    $this->basket->basket['order_path'] = 'no-login';
//                    $this->basket->basket['user_email'] = $this->ajax_data['user']['email'];
//                    $this->basket->setBasket(false);
//                    $this->basket->saveOrder();
//                }
                break;

            case 'login':
                break;

            case 'address':
                $this->basket->setPaymentData($this->ajax_data['address_data']);
                
                if(!$this->basket->addPaymentAddressData())
                {
                    $this->tpl_data['alerts'][] = $this->basket->errors;
                    $this->basket->setPaymentStep($this->ajax_data['step']);
                    $this->basket->setPayment();
                }
                break;

//            case 'payment':
//                $this->basket->setPaymentData($this->ajax_data['address_data']);
//                if(!$this->basket->addPaymentAddressData())
//                {
//                    $this->tpl_data['alerts'][] = $this->basket->errors;
//                    $this->basket->setPaymentStep($this->ajax_data['step']);
//                    $this->basket->setPayment();
//                }
//
//                $this->basket->setPaymentData($this->ajax_data['payment_data']);
//                $this->basket->addPaymentPaymentData();
//                $this->tpl_data['offer']['meta'] = $this->meta;
//                break;

            case 'payment':
            case 'combined':
                $this->basket->setPaymentData($this->ajax_data['address_data']);
                if(!$this->basket->addPaymentAddressData())
                {
                    $this->tpl_data['alerts'][] = $this->basket->errors;
                    $this->basket->setPaymentStep($this->ajax_data['step']);
                    $this->basket->setPayment();
                }
                // CUSTOM FAKTURA MERGE ERROR FIXER
                // $this->basket->getPaymentPaymentData();
                // if (!$this->basket->output['payment']['system_platnosci']) {

                //     $this->basket->setPaymentData($this->ajax_data['payment_data']);
                //     $this->basket->addPaymentPaymentData();
                // }
                $this->basket->setPaymentData($this->ajax_data['payment_data']);
                $this->basket->addPaymentPaymentData();
              


                $this->tpl_data['offer']['meta'] = $this->meta;
                break;
        }

        return $this->getPaymentStepData();
    }

    private function checkNoLoginData()
    {
        if (empty($this->ajax_data['user'])) {
            $this->tpl_data['alerts'][] = 'Brak danych użytkownika';
            return false;
        }

        if (empty($this->ajax_data['user']['email'])) {
            $this->tpl_data['alerts'][] = 'Brak adresu email';
            return false;
        }

        if ($this->ajax_data['user']['email'] !== $this->ajax_data['user']['emailConfirmation']) {
            $this->tpl_data['alerts'][] = 'Email i potwierdzenie email różnią się';
            return false;
        }

        if (!filter_var($this->ajax_data['user']['email'], FILTER_VALIDATE_EMAIL)) {
            $this->tpl_data['alerts'][] = 'Adres email jest nieprawidłowy';
            return false;
        }

        return true;
    }

    /**
     * Pobiera informacje do wygenerowania następnego kroku płatności
     * @return boolean
     */
    public function getPaymentStepData()
    {
        if(!$this->basket)
        {
            $this->basket = new BasketData;
        }
        
        $this->basket->getBasket();  // Pobranie danych koszyka
        $this->basket->getPayment(); // Pobranie danych płatności

        $this->tpl_data['logged'] = Sessions::IsLoggedIn();
        $this->tpl_conf['current_step'] = $this->basket->payment_step;
        // dd($this->basket->payment_step);
        // Przygotowanie kroku płatności
        switch($this->basket->payment_step)
        {
            default:
                return false;

            case 'path':
                if (isset($this->basket->basket['order_path']) && $this->basket->basket['order_path'] === 'no-login') {
                    $this->tpl_data['path'] = 'no-login';
                    $this->tpl_data['email'] = $this->basket->basket['user_email'];
                }
                break;

            // Krok: Koszyk
            case 'basket':
                $this->basket->getPaymentBasketData(true);
                $sniezynki = Sniezynka::getBasketGeneratedStamps( $this->basket->basket );

                if ( $this->tpl_data['logged'] )
                {
                    // Pobieranie kuponów użytkownika 
                    
                    $this->up = new UserProductsData();
                    $settings = array(
                        'set_active_coupons'        => true,
                        'add_coupon_offer_parentid' => true,
                    );

                    $this->up->setFormatSettings( $settings );
                    $this->up->setOutputProducts(array('kupony'));
                    $this->up->getProducts();
                    $kupony = $this->up->output['products']['kupony'];
                }
                else
                {
                    $kupony = false;
                }
                
                $basket = $this->basket->output;
                $isCouponsAvailable = false;
                if ( $kupony )
                {
                    foreach ( $basket['products'] as &$product )
                    {
                        foreach ( $kupony as $kupon )
                        {
                            if ( $kupon['oferta'] === 'ALL' || in_array( $product['parentid'], $kupon['parentid'] ) )
                            {
                                $isCouponsAvailable = true;
                                $product['coupons_available'] = true;
                                continue 2;
                            }
                        }
                    }
                }
            //    dd($isCouponsAvailable);
                $this->tpl_data['isCouponsAvailable'] = $isCouponsAvailable;
                $this->tpl_data['sniezynki'] = $sniezynki;
                $this->tpl_data['kupony']    = $kupony;
                $this->tpl_data['basket']    = $basket;
                break;

            // Pozostałość po poprzedniej wersji
            case 'login':
                break;

            // Krok: Adres
            case 'address':
                $this->basket->getPaymentAddressData();
                $this->tpl_data = array_merge( $this->tpl_data, $this->basket->output );
                $this->tpl_data['address_labels'] = self::AdresFormLabels(false);
                $this->tpl_data['countries']      = Additional::getCountriesList( LANG );
                break;

            // Krok: Płatność i dostawa
            case 'payment':
                $this->basket->getPaymentAddressData();
                $this->tpl_data = array_merge( $this->tpl_data, $this->basket->output );
                $this->tpl_data['address_labels'] = self::AdresFormLabels(false);
                $this->tpl_data['countries']      = Additional::getCountriesList( LANG );
                $this->basket->getPaymentPaymentData();
                $this->tpl_data = array_merge_recursive( $this->tpl_data, $this->basket->output );
                break;

            // Krok: Podsumowanie
            case 'sum':
                $this->basket->getPaymentBasketData(true);
                $this->tpl_data['basket'] = $this->basket->output;
                $this->tpl_data['basket']['accept_newsletter'] = $this->basket->basket['accept_newsletter'] ?? false;
                $this->tpl_data['offer']['meta'] = $this->meta;
                break;

            case 'combined':

                $this->basket->getPaymentBasketData(true);
                $sniezynki = Sniezynka::getBasketGeneratedStamps( $this->basket->basket );

                if ( $this->tpl_data['logged'] )
                {
                    // Pobieranie kuponów użytkownika

                    $this->up = new UserProductsData();
                    $settings = array(
                        'set_active_coupons'        => true,
                        'add_coupon_offer_parentid' => true,
                    );

                    $this->up->setFormatSettings( $settings );
                    $this->up->setOutputProducts(array('kupony'));
                    $this->up->getProducts();
                    $kupony = $this->up->output['products']['kupony'] ?? [];
                }
                else
                {
                    $kupony = false;
                }

                $basket = $this->basket->output;

                if ( $kupony )
                {
                    foreach ( $basket['products'] as &$product )
                    {
                        foreach ( $kupony as $kupon )
                        {
                            if ( $kupon['oferta'] === 'ALL' || in_array( $product['parentid'], $kupon['parentid'] ) )
                            {
                                $product['coupons_available'] = true;
                                continue 2;
                            }
                        }
                    }
                }

                $this->tpl_data['sniezynki'] = $sniezynki;
                $this->tpl_data['kupony']    = $kupony;
                $this->tpl_data['basket']    = $basket;


                //payment
                $this->basket->getPaymentAddressData();
                $this->tpl_data = array_merge( $this->tpl_data, $this->basket->output );
                $this->tpl_data['address_labels'] = self::AdresFormLabels(false);
                $this->tpl_data['countries']      = Additional::getCountriesList( LANG );
                $this->basket->getPaymentPaymentData();
                $this->tpl_data = array_merge_recursive( $this->tpl_data, $this->basket->output );
                break;
        }

        $this->tpl_conf['tpl'] = 'payment-' . $this->basket->payment_step;
        return true;
    }

    public function ajaxFinishPayment()
    {
        $this->basket = new BasketData();
        $this->order = new OrderData();
        $this->basket->getBasket();
        $newsletter = !(strtolower($_POST['acceptNewsletter'] ?? 'false') === 'false');
        $this->basket->basket['accept_newsletter'] = $newsletter;
        $this->basket->setBasket(false);
        $this->basket->getPayment();
        if ($this->basket->basket['order_path'] === 'no-login') {
            $passwd = md5(md5(random_bytes(16)) . SALT);
            $time = time();
            $email = $this->basket->basket['user_email'] . '|' . 'no-login-' . $time;
            if (!USER::Register($email, $passwd)) {
                $button_text = $this->app->Lang('Zaloguj się', 'F_Zamówienie');
                $response['error'] = '<span class="block">' . $this->app->Lang('Nie udało się utworzyć użytkownika.',
                        'F_Zamówienie') . '</span>';
                $response['error'] .= '<a class="btn btn-info btn-sm margtop10" href="' . SITE_URL . 'order/payment">' . $button_text . '</a>';
                $this->app->ADD('response', json_encode($response));
                $this->app->SetDecorator('Ajax');
                return;
            }
            $id_user = $this->sdb->last_id();
            USER::CreateUserMetaData(['item' => $id_user, 'rodzaj' => 'osobiste'], $id_user);
            $this->basket->basket['id_user'] = $id_user;
            $this->basket->setBasket(false);
            $this->basket->saveOrder();
        } else {
            $id_user = User::GetUserID();
        }


        if ((int)$id_user < 1) {
            $button_text = $this->app->Lang('Zaloguj się', 'F_Zamówienie');
            $response['error'] = '<span class="block">' . $this->app->Lang('Wykryto wylogowanie z systemu. Aby dokończyć płatność należy się ponownie zalogować.',
                    'F_Zamówienie') . '</span>';
            $response['error'] .= '<a class="btn btn-info btn-sm margtop10" href="' . SITE_URL . 'userlogin/loginpage">' . $button_text . '</a>';
            $this->app->ADD('response', json_encode($response));
            $this->app->SetDecorator('Ajax');
            return;

        }


        if (!$this->basket->checkBasket(true)) {
            $response['error'] = '';

            foreach ($this->basket->errors as $error) {
                $response['error'] = Tools::makeErrorsString($response['error'], $error);
            }
            $this->app->ADD('response', json_encode($response));
            $this->app->SetDecorator('Ajax');
            return;
        }

        if (Voucher::addExtraCodeForAttraction($this->basket->basket)) {
            $this->basket->setBasket(false);
        }

        // Przygotowanie danych powiadomienia o nowym zamówieniu

        $this->basket->getPaymentBasketData(true);
        $this->app->SetDecorator('Ajax');
        $this->app->AddDecoratorData('data', $this->basket->output);
        $this->app->AddDecoratorData('path', PROJECTDIR . '/application/admin_v4_0/views/modules/order/mail/new-order.tpl');
        $this->app->SetTemplate('modules/order/mail/new-order.tpl');
        $temp = new DecoratorSmarty($this->app);
        $order_data_html = $temp->output;
        $order_total = Tools::CC($this->basket->output['kw'], true);

        $this->order->getPayment();
        $this->order->setOrder($this->basket->basket);
        if (!$this->order->setOrderForPayment()) {
            $response['error'] = $this->app->Lang('Nie udało się przygotować płatności', 'F_Zamówienie');
            $this->app->ADD('response', json_encode($response));
            $this->app->SetDecorator('Ajax');
            return;
        }


        try {
            $partner = $this->app->settings['partnerdata'];
            $partner['partner_paygate_operator'] = null;
            if (
                !empty($this->app->settings['partner_paygate_operator']) &&
                $this->app->settings['partner_paygate_operator'] == $this->order->output['system_platnosci']['id']
            ) {

                $partner['partner_paygate_operator'] = $this->app->settings['partner_paygate_operator'];
                $partner['partner_paygate_config'] = $this->app->settings['partner_paygate_config'] ?? [];
            }
//            if (!empty($this->app->settings['partner_paygate_operator'])) {
//                $partner['partner_paygate_operator'] = $this->app->settings['partner_paygate_operator'];
//            } else {
//                $partner['partner_paygate_operator'] = null;
//            }
//            if (!empty($this->app->settings['partner_paygate_config'])) {
//                $partner['partner_paygate_config'] = $this->app->settings['partner_paygate_config'];
//            }
            $end_order = new Zamowienie($this->order->output);
            $end_order->prepOrder();
            $data = array(
                'id_zamowienia' => $end_order->id_zamowienia,
                'id_zamowienia_mgo' => $end_order->id_zamowienia_mgo,
                'system_platnosci' => $this->order->output['system_platnosci']['id'],
                'system_platnosci_partner_gate' => $this->order->output['system_platnosci']['partner_gate'] ?: 0,
                'adres_faktury' => $this->order->output['adres_faktury'],
                'adres_dostawy' => $this->order->output['adres_dostawy'],
                'adres_korespondencyjny' => $this->order->output['adres_korespondencyjny'],
                'koncowa_wartosc_zamowienia_brutto' => $this->order->output['wartosc_zamowienia']['koncowa_wartosc_zamowienia_brutto'],
                'bon' => $this->order->output['bon'],
                'currency' => $end_order->currency,
                'partner' => $partner
            );
            if ($this->order->output['user_email']) {
                $data['user_email'] = $this->order->output['user_email'];
            }
            $payment = new SetPayment($data['system_platnosci'], $data);
            $payment->AddSystem();
            $payment->system->MakePayment();
            $response['form_data'] = $payment->system->FormData();
            $end_order->processOrder();

            //  Mail

            require_once ENSETTINGSDIR . 'libs/Mailsend.class.php';
            Mailsend::activate();
            $partner_domain = str_replace('//', '/', $partner['domain'] . '/');
            $system = Tools::GetSystemSettings();
            $params['user'] = User::getUserID();
            $params['lang'] = $_SESSION['LANG'];
            if ($this->basket->basket['order_path'] === 'no-login') {
                $params['email'] = $this->basket->basket['user_email'];
            } else {
                $params['email'] = User::getUserEmail();
            }
//                    $params['lang']                     = User::getUserDefaultLang();
            $params['id_zamowienia'] = $end_order->id_zamowienia;
            $params[md5('%USER_FNAME%')] = User::GetUserFName();
            $params[md5('%SITE_URL%')] = $system['siteUrl']['value'];
            $params[md5('%SITE_NAME%')] = $system['siteName']['value'];
            $params[md5('%SITE_EMAIL%')] = $system['bokEmail']['value'];
            $params[md5('%ORDER_ID%')] = $end_order->id_zamowienia;
            $params[md5('%ORDER_DATE%')] = $end_order->data;
            $params[md5('%ORDER_TOTAL%')] = $order_total;
            $params[md5('%ORDER_DETAILS%')] = $order_data_html;
            $params[md5('%ORDER_PAYURL%')] = $partner_domain . 'order/repeatpayment?id=' . $end_order->id_zamowienia;

            //  Wysyłanie powiadomienia o nowym zamówieniu

            if (false == Mailsend::SendEmail('2_order_new', $params)) {
                $this->app->LogInsert('MailError', 0,
                    '2_order_received ' . $end_order->id_zamowienia . ': ' . Mailsend::$error);
            }

        } catch (Exception $e) {
            $response['error'] = $e->getMessage();
        }


      

        $this->app->ADD('response', json_encode($response));
        $this->app->SetDecorator('Ajax');
    }

  


    public function AjaxRepeatPayment()
    {
        $response['error'] = false;
        $id_order  = (int) $this->post['data']['id'];
      
        $id_system = $this->post['data']['system_platnosci'];
        $bon       = $this->post['data']['bon'];
        $bon_id    = 0;

        // fetch user id from order id 
      

        try
        {
            // log in user for repeat payment
            
            $id_mgo   = $this->rpGetMgoId( $id_order );


            $basket   = $this->rpGetMgoOrder( $id_mgo );
            $partner_gate_used = $basket['system_platnosci']['partner_gate'] ?? 0;

        
            if ($partner_gate_used) {
                $partner_config = PartnerService::GetMeta($basket['installation'], 'partner_paygate_config');
                if (empty($partner_config) || empty($partner_config['value'])) {
                    throw new Exception($this->app->Lang('Problem z systemem płatności.','F_Zamówienie'));
                }
            }


            $dpc      = $this->rpDelPaymentCost( $basket );
            $data     = $dpc['data'];
            $basket   = $dpc['basket'];

            // fetch user id from order
            $user_id = $basket['id_user'];
   

            // activate user to perform repeat payment
            $this->logInUserForRepeatPayment($user_id);

            // place check after the user is logged in
            $this->rpCheckData();

            $prev_sys = $dpc['data']['prev_system'];
            $next_sys = $this->rpPaySysData($id_system, $data['systemy_platnosci']);

            $prev_cost['brutto'] = $prev_sys['koszt_usera'];
            $prev_cost['vats']   = $prev_sys['vat'] / 100;
            $prev_cost = Tools::VAT( $prev_cost, 'float' );
            
            $next_cost['brutto'] = $next_sys['koszt_usera'];
            $next_cost['vats']   = $next_sys['vat'] / 100;
            $next_cost = Tools::VAT( $next_cost, 'float' );

            $sql = 'SELECT netto, brutto, vat FROM ' . TABLE_PREFIX . 'zamowienia ';
            $sql.= 'WHERE id = ' . $id_order . ' AND ' . ' id_usera = ' . User::GetUserID();
            $order_cost = $this->sdb->select_r( $sql );

            if ( $prev_cost['brutto'] > 0 )
            {
                $order_cost['brutto'] = Tools::CalcNf( $order_cost['brutto'], $prev_cost['brutto'], 'sub' );
                $order_cost['netto']  = Tools::CalcNf( $order_cost['netto'], $prev_cost['netto'], 'sub' );
                $order_cost['vat']    = Tools::CalcNf( $order_cost['vat'], $prev_cost['vatw'], 'sub' );
            }

            if ( $next_cost['brutto'] > 0 )
            {
                $order_cost['brutto'] = Tools::CalcNf( $order_cost['brutto'], $next_cost['brutto'] );
                $order_cost['netto']  = Tools::CalcNf( $order_cost['netto'], $next_cost['netto'] );
                $order_cost['vat']    = Tools::CalcNf( $order_cost['vat'], $next_cost['vatw'] );
                $basket['wartosc_zamowienia']['koncowa_wartosc_zamowienia_brutto'] = $order_cost['brutto'];
            }

            $kw = $order_cost['brutto'];

            // Sprawdzanie bonu
            if($bon)
            {
                Bon::delOrderBon($id_order);
                $user_bon = Bon::getBonSum(true, false);

                // bon check condition
                if($user_bon <= 0)
                {
                    throw new Exception($this->app->Lang('Wartość bonu uległa zmianie.','F_Zamówienie'));
                }

                if($id_system == PAYBON_ID)
                {
                    if($user_bon - $order_cost['brutto'] < 0)
                    {
                        throw new Exception($this->app->Lang('Wartość bonu niewystarczająca do opłacenia zamówienia','F_Zamówienie'));
                    }
                    else
                    {
                        $kwota = $order_cost['brutto'] * 100;
                    }
                }
                else
                {
                    if($user_bon - $order_cost['brutto'] >= 0)
                    {
                        throw new Exception($this->app->Lang('Wartość bonu uległa zmianie.','F_Zamówienie'));
                    }
                    else
                    {
                        $kwota = $user_bon * 100;
                        $kw    = Tools::CalcNF($kw, $user_bon, 'sub');
                    }
                }

                $bon_id = Bon::payForOrder($kwota, $id_order);
            }

            // Aktualizacja koszyka
            $basket['system_platnosci'] = $next_sys;
            $basket['bon'] = $bon;

            $mgo = MyMongo::activate();
            unset( $basket['_id'] );
            $mgo->replaceDocument( $basket, $id_mgo , 'orders' );

            // Aktualizacja płatności
            $client_ip = $_SERVER['REMOTE_ADDR'];
            $paydate   = time();

            $sql = 'UPDATE ' . TABLE_PREFIX . 'platnosci SET ';
            $sql.= 'pay_type = ' . $id_system . ', ';
            $sql.= 'client_ip = "' . $client_ip . '", ';
            $sql.= 'paydate = ' . $paydate . ', ';
            $sql.= 'bon = ' . $bon_id . ', ';
            $sql.= 'amount = ' . $kw * 100 . ' ';
            $sql.= 'WHERE item = ' . $id_order . ' ';
            $sql.= 'AND user = ' . User::GetUserID();
            $this->sdb->query($sql);

                // Aktualizacja zamówienia 
                $sql = 'UPDATE ' . TABLE_PREFIX . 'zamowienia SET ';
            $sql.= 'brutto = ' . $order_cost['brutto'] . ', ';
            $sql.= 'netto = ' . $order_cost['netto'] . ', ';
            $sql.= 'vat = ' . $order_cost['vat'] . ' ';
            $sql.= 'WHERE id = ' . $id_order . ' AND ' . ' id_usera = ' . User::GetUserID();
            $this->sdb->query( $sql );

            $order_data = [
                'id_zamowienia'                     => $id_order,
                'id_zamowienia_mgo'                 => $id_mgo,
                'system_platnosci'                  => $next_sys['id'],
                'adres_faktury'                     => $basket['adres_faktury'],
                'adres_dostawy'                     => $basket['adres_dostawy'],
                'koncowa_wartosc_zamowienia_brutto' => $kw,
                'bon'                               => $basket['bon'],
                'user_email' => $basket['user_email'] ?? User::ParseNoLoginEmail(User::GetUserEmail()),
            ];
         
            if ($partner_gate_used) {
                $order_data['system_platnosci_partner_gate'] = $partner_gate_used;
                $order_data['partner'] = ['partner_paygate_config' => $partner_config['value']];
            }

            $payment = new SetPayment( $next_sys['id'], $order_data );
          
            $payment->addSystem();
            $payment->system->repeat_payment = true;
            $response['form_data'] = $payment->system->FormData();
        }
        catch ( Exception $e )
        {
            $response['error'] = true;
            $response['error_msg'] = $e->getMessage();
        }
        // after performing tasks logout
        Sessions::Logout();

        $this->app->ADD('response', json_encode($response));
        $this->app->SetDecorator('Ajax');
    }

    public function ajaxCouponTransport()
    {
        $response = array('error' => false);
        $coupon['serial'] = $this->post['serial'];
        $this->basket = new BasketData();
        $this->basket->setCoupon($coupon);
        $this->basket->getPayment();
        $this->basket->getBasket();

        if(!$this->basket->checkTransportCoupon())
        {
            $response['error'] = '';
            foreach($this->basket->errors as $error)
            {
                $response['error'] = Tools::makeErrorsString($response['error'], $error);
            }            
        }
        else
        {
            $response['przewoznik'] = $this->basket->payment['przewoznik'];
            $response['kupon']      = $this->basket->coupon;
            $response['order']['value'] = $this->basket->getBasketPaymentValue();
        }


        $this->app->ADD('response', json_encode($response));
        $this->app->SetDecorator('Ajax');
    }

    public function ajaxDelTransCoupon()
    {
        $response = array('error' => false);
        $coupon['id'] = $this->post['id'];
        $this->basket = new BasketData();
        $this->basket->setCoupon($coupon);
        $this->basket->getPayment();
        $this->basket->getBasket();

        if(!$this->basket->deleteTransportCoupon())
        {
            $response['error'] = '';

            foreach($this->basket->errors as $error)
            {
                $response['error'] = Tools::makeErrorsString($response['error'], $error);
            }            
        }
        else
        {
            $response['przewoznik'] = $this->basket->payment['przewoznik'];
            $response['order']['value'] = $this->basket->getBasketPaymentValue();
        }

        $this->app->ADD('response', json_encode($response));
        $this->app->SetDecorator('Ajax');
    }

    public function ajaxChangeTransport()
    {
        $response = array('error' => false);
        $this->basket = new BasketData();
        $this->basket->getPayment();
        $this->basket->setPaymentData($this->post['przewoznik']);
        $this->basket->addPaymentTransport();
        $this->app->ADD('response', json_encode($response));
        $this->app->SetDecorator('Ajax');
    }
    private function logInUserForRepeatPayment($user_id)
    {
        try {
            Sessions::activate();
            Sessions::$logged_in = true;
            Sessions::$user_id = $user_id;
            //  temporarily save user data in User class
            User::SaveLoggedUserData();

        } catch (Exception $e) {
            file_put_contents('error.txt', $e->getMessage());
        }
    }

    public function repeatPayment()
    {
        
    
        try
        {
            $orderId = intval($this->get['id']);
            if($orderId < 1) {
                throw new Exception($this->app->Lang('Nieprawidłowe ID zamówienia.','F_Zamówienie'));
            }

            $id_mgo = $this->rpGetMgoId( $orderId );
            $basket = $this->rpGetMgoOrder( $id_mgo );
            $dpc    = $this->rpDelPaymentCost( $basket );
            $data   = $dpc['data'];
            $basket = $dpc['basket'];
           
            $user_id = $basket['id_user'];

            // activate session
            $this->logInUserForRepeatPayment($user_id);

            foreach ( $data['systemy_platnosci'] as &$system )
            {
                switch ( $system['id'] )
                {
                    default: break;
                        
                    case PAYDP_ID:
                        $system['logo_path'] = 'data/offers/Przelewy24_logo.png';
                        $system['podtytul'] = $this->app->Lang('Przelew internetowy','F_Zamówienie');
                        break;
                    
//                    case PAYDPCARD_ID:
//                        $system['logo_path'] = 'data/offers/Przelewy24_logo.png';
//                        $system['podtytul'] = $this->app->Lang('Karty kredytowe','F_Zamówienie');
//                        break;
                }
            }

            $data['kw'] = $basket['wartosc_zamowienia']['koncowa_wartosc_zamowienia_brutto'];
            $data['id'] = $orderId;
            $data['bon_sum']  = 0;
            $data['bon_data'] = Tools::getBonData();

            $bon_used = Bon::getOrderBon($data['id']);

            if(abs($bon_used['kwota']) > 0)
            {
                $data['bon_sum']  = Tools::CalcNF(abs($bon_used['kwota']), 100, 'div');
            }

            $data['bon_sum']  = Tools::CalcNF(Bon::getBonSum(true,false), floatval($data['bon_sum']));
            $data['bon_used'] = $basket['bon'];

            if($data['prev_system']['id'] == PAYBON_ID)
            {
                $data['bon_selected'] = true;
            }
            else
            {
                $data['bon_selected'] = false;
            }
            // after perfoming tasks logout
            Sessions::Logout();

            // Tools::PA($data); die();
            $this->tpl_data = $data;
        }
        catch ( Exception $e )
        {
            $this->alerts['danger'][] = $e->getMessage();
        }

        $this->display('repeat-payment');
    }

    function rpCheckData()
    {
        if( (int) User::GetUserID() < 1)
        {
            throw new Exception($this->app->Lang('Aby opłacić zamówienie musisz być zalogowany.','F_Zamówienie'));
        }

        if( (int) $this->post['data']['id'] < 1)
        {
            throw new Exception($this->app->Lang('Nieprawidłowe ID zamówienia.','F_Zamówienie'));
        }

        if( (int) $this->post['data']['system_platnosci'] < 1)
        {
            throw new Exception($this->app->Lang('Nieprawidłowy system płatności.','F_Zamówienie'));
        }

        return true;
    }

    function rpPaySysData( $id_system, $payment_systems )
    {
        if($id_system != PAYBON_ID)
        {
            foreach ( $payment_systems as &$system )
            {
                if ( $system['id'] == $id_system )
                {
                    $payment_system = array_merge( $system, Tools::GetPartnerMetadata( $system['id'] ) );
                }
            }
        }
        else
        {
            $bon_data = Tools::GetBonData();
            $payment_system = Tools::GetPartnerMetadata($id_system);
            $payment_system['id']          = $id_system;
            $payment_system['logo_path']   = '';
            $payment_system['nazwa']       = $bon_data['shortname'];
            $payment_system['podtytul']    = $bon_data['name'];
            $payment_system['koszt_usera'] = 0;
        }
            
        if ( !$payment_system )
        {
            throw new Exception( $this->app->Lang('Nie pobrano danych systemu płatności','F_Zamówienie'));
        }

        return $payment_system;
    }

    function rpDelPaymentCost( $basket )
    {
        $offer_data = new OfferData();
        $offers_memory = array();
        $data['systemy_platnosci'] = array();

        if ( $basket['system_platnosci']['koszt_usera'] > 0 )
        {
            $kw = &$basket['wartosc_zamowienia']['koncowa_wartosc_zamowienia_brutto'];
            $ku = &$basket['system_platnosci']['koszt_usera'];
            $basket['wartosc_zamowienia']['koncowa_wartosc_zamowienia_brutto'] = Tools::CalcNf( $kw, $ku, 'sub' );
        }

        $data['prev_system'] = $basket['system_platnosci'];

        unset( $basket['system_platnosci'] );

        foreach ( $basket['zamowienia'] as $order )
        {
            if ( !$offers_memory[ $order['parentid'] ] )
            {
                $offer_data->setParentId( $order['parentid'] );
                $offer_data->setOperators( 'offer_transport_payment' );
                $offer_data->getOffer();
                $offer_data->setSingle();
                $offers_memory[ $order['parentid'] ] = $offer_data->output;
                
                if ( ! $data['systemy_platnosci'] )
                {
                    $data['systemy_platnosci'] = $offers_memory[ $order['parentid'] ]['systemy_platnosci'];
                }
                else
                {
                    $data['systemy_platnosci']   = array_intersect_key( $data['systemy_platnosci'], $offers_memory[ $order['parentid'] ]['systemy_platnosci'] );
                    $data['koszt_usera_platnosc'] = 0.00;
                }
            }
        }

        $return['basket'] = $basket;
        $return['data'] = $data;
        return $return;
    }

    function rpGetMgoOrder( $id_mgo )
    {
        $mdb = Mymongo::activate();
        $basket = $mdb->mget_order_details( $id_mgo );

        if ( !$basket )
        {
            throw new Exception( $this->app->Lang('Nie udało się pobrać danych zamówienia','F_Zamówienie'));
        }

        return $basket;
    }

    function rpGetMgoId( $id )
    {
        if ( !$id )
        {
            throw new Exception( $this->app->Lang('Błędne id zamówienia','F_Zamówienie'));
        }

        $sql = 'SELECT id_zamowienia_mgo FROM ' . TABLE_PREFIX . 'zamowienia ';
        $sql.= 'WHERE id = ' . $id . ' AND status = -1';
        $res = $this->sdb->select_r( $sql );

        if ( !$res )
        {
            throw new Exception( $this->app->Lang('Nie odnaleziono zamówienia','F_Zamówienie'));
        }

        return (string) $res['id_zamowienia_mgo'];
    }

    // helper method for voucher generation
    private function getMongoId($id)
    {
        if ( !$id )
        {
            throw new Exception( $this->app->Lang('Błędne id zamówienia','F_Zamówienie'));
        }

        $sql = 'SELECT id_zamowienia_mgo FROM ' . TABLE_PREFIX . 'zamowienia ';
        $sql.= 'WHERE id = ' . $id ;
        $res = $this->sdb->select_r( $sql );

        if ( !$res )
        {
            throw new Exception( $this->app->Lang('Nie odnaleziono zamówienia','F_Zamówienie'));
        }

        return (string) $res['id_zamowienia_mgo'];
    }

    // generate voucher download link
    private function generateAfterpayVoucherDownloadLink($payment)
    {
        $user_id = $payment->dane_platnosci['user'];
        $order_id = $payment->dane_platnosci['item'];
        // file_put_contents('user_id_order_id.txt', $user_id . ' ' . $order_id);
        if($user_id != 0){
            $voucher = Voucher::GetDownloadPDF($order_id, 'user', $user_id);
            file_put_contents('afterPay1.txt', $voucher);
        } else {
            try {
                $id_mgo   = $this->getMongoId( $order_id );
                // file_put_contents('idmgo.txt', $id_mgo);
                $basket   = $this->rpGetMgoOrder( $id_mgo );
                $user_id = $basket['id_user'];
                // file_put_contents('basket.txt', json_encode($basket));
                $voucher = Voucher::GetDownloadPDF($order_id, 'user', $user_id);
            } catch (Exception $e) {
                file_put_contents('error.txt', $e->getMessage());
                return  ['link' => null, 'status' => 0];
            }
        }

        return $voucher;

    }

    private function displayVoucherDownloadLink($voucher, $payment)
    {

       $isVoucherAvailable = $voucher['status'] == 1;

        if ($isVoucherAvailable) {
            $this->tpl_data['voucher'] = $voucher;
        }

        return $isVoucherAvailable ?  $voucher['link'] : '';

    }

    public function afterPay()
    {
        $payment = new SetPayment();
        switch(strtoupper($this->get['type']))
        {
            default:
                try
                {
//                    $payment = new SetPayment();
                    $payment->getPaymentData($this->get['userdata']);
                    $payment->addSystem();
                    $payment->checkResponse($this->get);
                    $payment->app = $this->app;
                    $result = $payment->finishPaymentUser();
                    $success = true;
                }
                catch(Exception $e)
                {
                    $errors = $e->getMessage();
                    $success = false;
                }
                break;
            case 'P24':
                $again = true;
                for ($a=0;$a<3;$a++){
                    sleep(3);
                    try
                    {
//                        $payment = new SetPayment();
                        $payment->getPaymentData($this->get['userdata']);
                        if($payment->dane_platnosci['status'] == 99){
                            $success = true;
                            break;
                        }
                    }
                    catch(Exception $e)
                    {
                        $errors = $e->getMessage();
                        $success = false;
                    }


                }
                if(!$success){
                    $this->alerts['success'][] = $this->app->Lang('Płatność jest przetwarzana. Proszę sprawdzić jej status w panelu użytkownika','F_Zamówienie');
                    $this->main();
                    return;

                }
                break;
        }

        if($success)
        {

        
           $voucher =  $this->generateAfterpayVoucherDownloadLink($payment);

           $voucherLinkText = $this->displayVoucherDownloadLink($voucher, $payment);

            $this->tpl_data['voucher_alert']['body'] = $this->app->Lang('<h3>Płatność została pomyślnie zakończona.</h3> </br> <p style="font-size: 15px"> Zapisz lub zrób zdjęcie poniższego numeru zamówienia – będzie on potrzebny podczas kontaktu z infolinią, jeśli nie otrzymasz wiadomości e-mail z potwierdzeniem. </br>
Status zakupionych usług możesz sprawdzić na swoim koncie Klienta. W przypadku zakupu voucherów będą one również dostępne do pobrania w Twoim koncie.</p>
', 'F_Zamówienie');
            $this->tpl_data['voucher_alert']['link'] = $voucherLinkText;
        
        }
        else
        {
            $this->alerts['danger'][] = $this->app->Lang('Podczas operacji wystąpił błąd.','F_Zamówienie');
            $this->alerts['danger'][] = $errors;
        }

        $sql = 'SELECT currbrutto, currency FROM ' . TABLE_PREFIX . 'zamowienia ';
        $sql.= 'WHERE id = ' . $payment->dane_platnosci['item'];
        $fbq = $this->sdb->select_r($sql);

        $this->tpl_data['fbq_purchase']['amt'] = $fbq['currbrutto'];
        $this->tpl_data['fbq_purchase']['cur'] = strtoupper($fbq['currency']);

        $this->main();
        return true;
    }

    public function ajaxGetBasketCont()
    {
        $this->basket = new BasketData();
        $this->basket->getBasket();
        $this->basket->getWidgetData();
//        dd($this->basket->widget);
        $params['widget'] = $this->basket->widget;
        $params['current_tpl'] = $this->post['current_tpl'];
        $this->app->SetDecorator('Ajax');
        $this->app->AddDecoratorData('params', $params);
        $this->app->SetTemplate('widgets/basket-container.tpl');
        $resp = new DecoratorSmarty($this->app);
        echo $resp->output;
    }
  private function recursive_sanitize_for_json($data)
    {
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                $data[$key] = $this->recursive_sanitize_for_json($value);
            }
        } elseif (is_string($data)) {
            $data = preg_replace('/[^\P{C}\t\n\r]/u', '', $data);
        }
        return $data;
    }

    public function ajaxGetUpdatedBasket()
    {
        ob_clean();
        $this->basket = new BasketData();
        $this->basket->getBasket();
        $this->basket->getWidgetData();

        $final_ajax_response_data = [];
        $widget_data_for_response = [];

        if (isset($this->basket->widget['payment'])) {
            $widget_data_for_response['payment'] = $this->basket->widget['payment'];
        }
        if (isset($this->basket->widget['amount'])) {
            $widget_data_for_response['amount'] = $this->basket->widget['amount'];
        }

        if (isset($this->basket->widget['orders']) && is_array($this->basket->widget['orders'])) {
            $lean_orders_by_type = [];
            foreach ($this->basket->widget['orders'] as $product_type => $order_items_of_type) {
                if (is_array($order_items_of_type)) {
                    $lean_items_for_current_type = [];
                    foreach ($order_items_of_type as $original_item) {
                        $lean_item = [];

                        if (isset($original_item['id_basket'])) {
                            $lean_item['id_basket'] = $original_item['id_basket'];
                        }
                        if (isset($original_item['amount'])) {
                            $lean_item['amount'] = $original_item['amount'];
                        }

                        if (isset($original_item['main_product']) && is_array($original_item['main_product'])) {
                            $original_main_product = $original_item['main_product'];
                            $lean_main_product = [];

                            if (isset($original_main_product['identyfikator'])) {
                                $lean_main_product['identyfikator'] = $original_main_product['identyfikator'];
                            }
                            if (isset($original_main_product['bsid'])) {
                                $lean_main_product['bsid'] = $original_main_product['bsid'];
                            }
                            if (isset($original_main_product['nazwa'])) {
                                $lean_main_product['nazwa'] = $original_main_product['nazwa'];
                            }
                            if (isset($original_main_product['partner_id'])) {
                                $lean_main_product['partner_id'] = $original_main_product['partner_id'];
                            }
                            
                            if ($product_type === 'kupon') {
                                if (isset($original_main_product['id_emisji'])) {
                                    $lean_main_product['id_emisji'] = $original_main_product['id_emisji'];
                                }
                                if (isset($original_main_product['kwota_brutto'])) {
                                    $lean_main_product['kwota_brutto'] = $original_main_product['kwota_brutto'];
                                }
                            }
                            $lean_item['main_product'] = $lean_main_product;
                        }
                        $lean_items_for_current_type[] = $lean_item;
                    }
                    $lean_orders_by_type[$product_type] = $lean_items_for_current_type;
                } else {
                    $lean_orders_by_type[$product_type] = $order_items_of_type; 
                }
            }
            $widget_data_for_response['orders'] = $lean_orders_by_type;
        }
        
        $final_ajax_response_data['widget'] = $widget_data_for_response;
        $final_ajax_response_data['orders_count'] = count($this->basket->basket['zamowienia']);
        
        $this->ajax_resp['data'] = $final_ajax_response_data;

        $sanitized_ajax_resp = $this->recursive_sanitize_for_json($this->ajax_resp);
        
        $json_flags = JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE;
        if (defined('JSON_INVALID_UTF8_SUBSTITUTE')) {
            $json_flags |= JSON_INVALID_UTF8_SUBSTITUTE;
        }

        $json_output = trim(json_encode($sanitized_ajax_resp, $json_flags));
        
        $this->app->ADD('response', $json_output);
        $this->app->SetDecorator('Ajax');
    }


    public function removeCoupon()
    {
        $serial = $this->post['serial'];
        $bid = $this->post['bid'];
        $pid = $this->post['pid'];
        $ptype =$this->post['ptype'];
        $resp['status'] = false;
        $resp['error'] = false;

        $this->basket = new BasketData;
        $this->basket->getBasket();
        $ret = $this->basket->removeCoupon($serial, $bid, $pid, $ptype);
        if(false === $ret)
        {
            $resp['error'] = $this->app->Lang('Brak dopasowanych kuponów','F_Zamówienie');
            echo json_encode($resp);
            die();
        }

        $emission = (new KuponyEmisje())->findOne($ret['id_emisji']);
        if (null === $emission) {
            $emission = new stdClass();
            $emission->add_to_all = false;
        }
        $allToOneCounter = $this->basket->AddToAllKuponsDecrease($ret['id_emisji']);

        $kp = new Kupon;
        if (null === $allToOneCounter || (bool) $emission->add_to_all !== true) {
            $r = $kp->releaseCoupon($ret['serial'], $ret['rodzaj_kuponu'], $ret['id']);
            if(false === $r)
            {
                $resp['error'] = $this->app->Lang('Bląd odblokowania kuponu','F_Zamówienie');
                echo json_encode($resp);
                die();
            }
        }

        $html_coupon = '';
        if ((false !== $uid = User::GetUserID()) && $kp->isUserCoupon($serial, $uid)) {
            $emission = $this->sdb->select_r('SELECT * FROM ' . TABLE_PREFIX.'kupony_emisje WHERE id = ' . $ret['id_emisji']);
            $html_data_kupon = [
                'serial' => $serial,
                'rodzaj_kuponu' => $ret['rodzaj_kuponu'],
                'typ_produktu' => $emission['typ_produktu'],
                'wartosc_jedn_kuponu' => $ret['wartosc_jedn_kuponu'],
                'typ_wartosci' => $ret['typ_wartosci']
            ];
            $this->app->SetDecorator('Smarty');
            $this->app->AddDecoratorData('kupon', $html_data_kupon);
            $this->app->SetTemplate('modules/order/payment/parts/kupon-user.tpl');
            $html_coupon = (new DecoratorSmarty($this->app))->output;
        }

        $this->basket->setBasket(false);
        $resp['status'] = true;
        $resp['htmlUserCoupon'] = $html_coupon;
        $resp['html'] = $this->DrawCombinedProductsFragment();
        $resp['value'] = $this->basket->getBasketPaymentValue();
        echo json_encode($resp);
        die();
    }

    public function setCoupon()
    {
        $serial = $this->get['serial'];
        $bid = (int) $this->get['bid'];
        $pid = $this->get['pid'];
        $ptype = $this->get['ptype'];
        $ctype = $this->get['ctype'] ?? null;
        $resp['status'] = false;
        $resp['error'] = false;

        $kuponModel = new \App\DTO\KuponCheckDTO();
        $kuponModel->getActive($serial, 'serial');
        if(null === $kuponModel->getKuponModel()) {
            $resp['error'] = $this->app->Lang('Kupon użyty lub błędny numer kuponu','F_Zamówienie');
            echo json_encode($resp);
            die();
        }

        $emission = (new KuponyEmisje())->findOne($kuponModel->getKuponModel()->id_emisji);
        if(null === $emission || !$emission->isActive())
        {
            $resp['error'] = $this->app->Lang('Kupon użyty lub błędny numer emisji','F_Zamówienie');
            echo json_encode($resp);
            die();
        }

        $this->basket = new BasketData;
        $this->basket->getBasket();
        $possible = $emission->getPossibleProducts($this->basket->getBasketAsDTO(), $kuponModel);

        if(empty($possible)) {
            $resp['error'] = $this->app->Lang('Kupon nie może być użyty w zamówieniu','F_Zamówienie');
            echo json_encode($resp);
            die();
        }


        $addcoupon = $this->basket->addCouponV2($kuponModel, $emission, $bid, $pid, $ptype);
        $kuponModel->getKuponModel()->reserveCoupon($this->basket->basket['bid'], $emission);
        if (false === $addcoupon['status']) {
            $resp['error'] = implode(', ', $this->basket->errors);
        }
        $resp['status'] = $addcoupon['status'];
        $resp['kupon'] = $addcoupon['kupon'];
        $resp['basket'] = $addcoupon['basket'];
        $this->basket->setBasket(false);
        $resp['value'] = $this->basket->getBasketPaymentValue();
        $resp['html'] = $this->DrawCombinedProductsFragment();
        echo json_encode($resp);
        die();
    }

    protected function DrawCombinedProductsFragment()
    {
        if(!$this->basket)
        {
            $this->basket = new BasketData;
        }
        $this->basket->getPayment();
        $this->basket->setPaymentStep('combined');
        $this->basket->setPayment();
        $this->getPaymentStepData();
        $this->app->SetDecorator('Smarty');
        $this->app->AddDecoratorData('data', $this->tpl_data);
        $this->app->SetTemplate('modules/order/payment/parts/products.tpl');
        return (new DecoratorSmarty($this->app))->output;
    }


    public function GetCouponListV2()
    {
        $serial = $_REQUEST['serial'];
        $bid = $_REQUEST['bid'];
        $resp['error'] = false;

        $kuponModel = (new \App\DTO\KuponCheckDTO())->getActive($serial, 'serial');
        if(null === $kuponModel->getKuponModel()) {
            $resp['error'] = $this->app->Lang('Kupon użyty lub błędny numer kuponu','F_Zamówienie');
            echo json_encode($resp);
            die();
        }

        $emission = (new KuponyEmisje())->findOne($kuponModel->getKuponModel()->id_emisji);
        if(null === $emission || !$emission->isActive())
        {
            $resp['error'] = $this->app->Lang('Kupon użyty lub błędny numer emisji','F_Zamówienie');
            echo json_encode($resp);
            die();
        }


        $this->basket = new BasketData;
        $this->basket->getBasket();
        if ($emission->typ_produktu === 'TRANSPORT') {
            $this->basket->setCoupon(['serial' => $serial]);
            $this->basket->getPayment();

            if(!$this->basket->checkTransportCoupon())
            {
                $response['error'] = '';
                foreach($this->basket->errors as $error)
                {
                    $response['error'] = Tools::makeErrorsString($response['error'], $error);
                }
            }
            else
            {
                $response['przewoznik'] = $this->basket->payment['przewoznik'];
                $response['kupon']      = $this->basket->coupon;
                $response['order']['value'] = $this->basket->getBasketPaymentValue();
                $response['coupon_type'] = 'transport';
            }

            $this->app->ADD('response', json_encode($response));
            $this->app->SetDecorator('Ajax');
            return;
        }

        $stos = $this->basket->getBasketAsDTO();
        $possible = $emission->getPossibleProducts($stos, $kuponModel);
        if(empty($possible)) {
            $resp['error'] = $this->app->Lang('Kupon nie może być użyty w zamówieniu','F_Zamówienie');
            echo json_encode($resp);
            die();
        }

        if ($emission->add_to_all === 0) {
            $this->app->ADD('response', json_encode([
                'coupon_type' => 'product',
                'data' => $possible
            ]));
            $this->app->SetDecorator('Ajax');
            return;
        }

        $response = $this->basket->AddToAllCouponV2($kuponModel, $emission, $possible);
        $this->basket->AddToAllKuponsIncrease($emission->id, $kuponModel->getKuponModel()->id, count($response));
        $kuponModel->getKuponModel()->reserveCoupon($this->basket->basket['bid'], $emission);
        $this->basket->setBasket(false);
        $resp['status'] = !empty($response);
        $resp['coupon_type'] = 'product';
        $resp['skipPossibleList'] = true;
        $resp['value'] = $this->basket->getBasketPaymentValue();
        $resp['html'] = $this->DrawCombinedProductsFragment();
        $resp['response'] = $response;
        $this->app->ADD('response', json_encode($resp));
        $this->app->SetDecorator('Ajax');

    }

    /**
     * Wyświetlanie template
     * @param string $tpl Nazwa pliku tpl
     */
    private function display($tpl)
    {
        if(!$this->widget_header_data['title'])
        {
            $this->widget_header_data['title'] = SITE_NAME;
        }

        if (empty($this->tpl_data['usercards'])) {
            $this->tpl_data['usercards'] = [];
        }

        $partner_meta = Tools::GetPartnerMetadata( INSTALLATION );
        $this->widget_basket_data['current_tpl'] = $tpl;
        $this->app->AddWidgetsData('topnav', $this->meta['header']);
        $this->app->AddWidgetsData('footer', $this->meta['footer']);
        $this->app->AddWidgetsData('header', $this->widget_header_data);
        $this->app->AddWidgetsData('basket', $this->widget_basket_data);
        $this->app->AddWidgetsData('breadcrumbs');
        $this->app->AddWidgetsData('alerts', $this->alerts);
        $this->app->AddDecoratorData('data', $this->tpl_data);
        $this->app->AddDecoratorData( 'partner_meta', $partner_meta );
        $this->app->AddDecoratorData('current_tpl', $tpl);
        $this->app->AddDecoratorData('tpl', $this->tpl_conf['tpl']);
        $this->app->AddDecoratorData('dir', $this->tpl_conf['dir']);
        $this->app->AddDecoratorData('tpl_conf', $this->tpl_conf);
        $this->app->SetTemplate('modules/order/' . $tpl . '.tpl');
    }

    public static function AdresFormLabels( $all = true ){
        $labels['imie']     = App::_Lang('Imię','F_Labels');
        $labels['nazwisko'] = App::_Lang('Nazwisko','F_Labels');
        $labels['adres']    = App::_Lang('Adres','F_Labels');
        $labels['miasto']   = App::_Lang('Miejscowość','F_Labels');
        $labels['kod']      = App::_Lang('Kod pocztowy','F_Labels');
        $labels['kraj']     = App::_Lang('Kraj','F_Labels');
        $labels['email']    = App::_Lang('Email','F_Labels');
        $labels['tel']      = App::_Lang('Nr telefonu','F_Labels');

        if ( $all )
        {
            $labels['firma'] = App::_Lang('Firma','F_Labels');
            $labels['nip']   = App::_Lang('NIP','F_Labels');
        }
        return $labels;
    }

    function days_in_month($month, $year)

    {

        // calculate number of days in a month

        return $month == 2 ? ($year % 4 ? 28 : ($year % 100 ? 29 : ($year % 400 ? 28 : 29))) : (($month - 1) % 7 % 2 ? 30 : 31);

    }

    public function getCalendarData() 
    {
        $response = ['error' => false];

        // *** Generate mock calendar data *** 
        $prices = [];
        // Get year and month (you'll need to determine how to get this)
        $year = date('Y'); // Example: Current year 
        $month = date('m'); // Example: Current month

        // Generate random prices for each day of the month
        for ($day = 1; $day <= $this->days_in_month( $month, $year); $day++) {
            $date = date('Y-m-d', mktime(0, 0, 0, $month, $day, $year)); 
            $prices[$date] = rand(50, 500); // Random prices between 50 and 500
        }

        $response['calendarData'] = $prices;

        $this->app->ADD('response', json_encode($response));
        $this->app->SetDecorator('Ajax');
    }

}

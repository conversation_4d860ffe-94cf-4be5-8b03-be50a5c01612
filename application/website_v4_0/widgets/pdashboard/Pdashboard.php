<?php

/**
 * Obsługa menu na stronie partnera sprzedażowego
 * @package Application
 * @subpackage Widgets
 */
class Pdashboard 
{

    private $sdb    = null;
    private $app    = null;
    private $config = array();
    public  $output = null;

    public function __construct( $app, $params = array() ) 
    {
        if ( ! is_array( $params ) )
        {
            $params = array();
        }
        $this->sdb = Database::activate();
        $this->app = &$app;
        $params['page'] = $this->app->urlparam['a'];
        $params = $this->mkconfig( $params );
        $this->DisplayDashboard( $params );
    }

    private function mkconfig( $params ) 
    {
        return array_merge( $this->config, $params );
    }

    private function DisplayDashboard( $params ) 
    {
        $return = array(
            'template' => "widgets/pdashboard.tpl",
            'data'     => $params
        );
        
        $this->output = $return;
    }

}

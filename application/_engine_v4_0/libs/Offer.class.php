<?php

/* 
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

class Offer {
    private $mgo;
    private $en;
    private $app;
    protected ?string $parentid = null;
    protected $offersrc =array();
    protected $karnety =array();
    protected $karty =array();
    protected $vouchery =array();
    protected $virtualp =array();
    protected $ubezpieczenia =array();
    protected $lang =false;
    protected $objects = array();

    public function __construct(&$app) {
        $this->app = &$app;  
        $this->mgo = Mymongo::activate();
        $this->en = Database::activate();
        $this->lang = LANG;
    }
    
    public function Reset(){
        $this->parentid = null;
        $this->offersrc =array();
        $this->karnety =array();
        $this->karty =array();
        $this->vouchery =array();
        $this->virtualp =array();
        $this->ubezpieczenia =array();
    }

    public function getParentid(): ?string
    {
        return $this->parentid;
    }
    
    public function GetOffer(){
        return $this->offersrc;
    }
    
    /**
     * 
     * @param type $parentid
     */
    public function SetOffer($parentid){
        if($this->parentid !== $parentid){
            $this->Reset();
            $this->offersrc = $this->mgo->mget_offer_details($parentid);
            $this->karnety = $this->offersrc['karnety'];
            $this->karty = $this->offersrc['karty'];
            $this->vouchery = $this->offersrc['vouchery'];
            $this->virtualp = $this->offersrc['produkty_wirtualne'];
            $this->ubezpieczenia = $this->offersrc['ubezpieczenia'];
            $this->parentid = $this->offersrc['parentid'];
        }
    }
    
    public function GetDataOd() {
        return $this->offersrc['data_od']['sec'];
    }
    
    public function GetDataOdDate() {
        return date('Y-m-d',$this->offersrc['data_od']['sec']);
    }
    
    public function GetDataOdDateTime() {
        return date('Y-m-d H:i:s',$this->offersrc['data_od']['sec']);
    }
    
    public function GetDataDo() {
        return $this->offersrc['data_do']['sec'];
    }
    
    public function GetDataDoDate() {
        return date('Y-m-d',$this->offersrc['data_do']['sec']);
    }
    
    public function GetDataDoDateTime() {
        return date('Y-m-d H:i:s',$this->offersrc['data_do']['sec']);
    }
    
    public function GetForkFrom() {
        return $this->offersrc['forkfrom'];
    }
    
    public function GetForkParentId() {
        return $this->offersrc['forkparentid'];
    }
    
    public function GetStatusId() {
        return $this->offersrc['status']['id'];
    }
    
    public function GetStatusNazwa() {
        return $this->offersrc['status']['nazwa'];
    }
    
    public function GetStatus() {
        return $this->offersrc['status'];
    }
    
    public function Offer() {
        return (object) $this->offersrc;
    }

    
    public function GetProductByIdx($type,$idx){
        $fname = 'Get'.ucfirst($type).'ByIDX';
        return $this->$fname($idx);
    }
    
    public function GetProductById($type,$id){
        $fname = 'Get'.ucfirst($type).'ByID';
        return $this->$fname($id);
    }
    
    public function GetVoucherByIDX($idx){
        return $this->vouchery[$idx];
    }
    
    public function GetVoucherByID($identyfikator){
        foreach($this->vouchery as $v){
            if($v['identyfikator'] === $identyfikator){
                return $v;
            }
        }
    }
    
    public function GetKartaByIDX($idx){
        return $this->karty[$idx];
    }
    
    public function GetKartaByID($identyfikator){
        foreach($this->karty as $v){
            if($v['identyfikator'] === $identyfikator){
                return $v;
            }
        }
    }
    
    public function GetKarnety(){
        return $this->karnety;
    }
    
    public function GetKarnetByIDX($idx){
        return $this->karnety[$idx];
    }
    
    public function GetKarnetByID($identyfikator){
        foreach($this->karnety as $v){
            if($v['identyfikator'] === $identyfikator){
                return $v;
            }
        }
    }
    
    public function GetVirtualProducts(){
        return $this->virtualp;
    }
    
    public function GetVirtualByIDX($idx){
        return $this->virtualp[$idx];
    }
    
    public function GetVirtualByID($identyfikator){
        foreach($this->virtualp as $v){
            if($v['identyfikator'] === $identyfikator){
                return $v;
            }
        }
    }
    
    public function GetUbezpieczenieByIDX($idx){
        return $this->ubezpieczenia[$idx];
    }
    
    public function GetUbezpieczenieByID($identyfikator){
        foreach($this->ubezpieczenia as $v){
            if($v['identyfikator'] === $identyfikator){
                return $v;
            }
        }
    }

    public function getOfferMeta($parter_sp_id)
    {
        $now = new DateTime();
        $where = array(
            'status.id'       => 5,
            'current'         => 1,
            'partnerzy_sp.id' => $parter_sp_id,
            'data_od' => [ '$lte' => new MongoDB\BSON\UTCDateTime($now)],
            'data_do' => [ '$gte' => new MongoDB\BSON\UTCDateTime($now)],
        );

        $cursor = $this->mgo->db->offers->findOne($where);
        return $this->mgo->Mgo2array($cursor);
    }


    public function getProductVariantName($offerId, $productId, $ptype, $variantId): string
    {
        if (empty($offerId) || empty($productId) || empty($variantId)) {
            return '-';
        }

        if (null === $this->parentid || $this->parentid !== $offerId) {
            $this->SetOffer($offerId);
        }

        try {
            switch ($ptype) {
                case 'karnety':
                    $product = $this->GetKarnetByID($productId);
                    foreach ($product['wariant_cenowy'] as $variant) {
                        if ($variant['bsid'] === $variantId) {
                            return $variant['nazwa'] . ' (' . $variantId . ')';
                        }
                    }
                    break;
                case 'vouchery':
                    $product = $this->GetVoucherByID($productId);
                    foreach ($product['wariant_cenowy'] as $variant) {
                        if ($variant['bsid'] === $variantId) {
                            return $variant['nazwa'] . ' (' . $variantId . ')';
                        }
                    }
                    break;
            }
        } catch (Exception $e) {
            return 'Błąd pobierania nazw (' . $e->getMessage() . ')';
        }

        return 'Wariant ID: ' . $variantId;
    }

    public function getProductName($offerId, $productId, $ptype): string
    {
        if (empty($offerId) || empty($productId)) {
            return '-';
        }

        if (null === $this->parentid || $this->parentid !== $offerId) {
            $this->SetOffer($offerId);
        }

        try {
            switch ($ptype) {
                case 'karnety':
                    $product = $this->GetKarnetByID($productId);
                    return $product['nazwa'][$this->lang];
                case 'vouchery':
                    $product = $this->GetVoucherByID($productId);
                    return $product['nazwa'][$this->lang];
            }
        } catch (Exception $e) {
            return 'Błąd pobierania nazw (' . $e->getMessage() . ')';
        }

        return 'Produkt ID: ' . $productId;
    }

    public function getOfferName($offerId): string
    {
        if (empty($offerId)) {
            return '-';
        }

        if (null === $this->parentid || $this->parentid !== $offerId) {
            $this->SetOffer($offerId);
        }

        try {
            return $this->offersrc['nazwa'][$this->lang] ?? 'Oferta ID: ' . $offerId;
        } catch (Exception $e) {
            return 'Błąd pobierania oferty (' . $e->getMessage() . ')';
        }
    }

    public function getOfferProductsList($offerId, $ptype, bool $full = false): array
    {
        if (null === $this->parentid || $this->parentid !== $offerId) {
            $this->SetOffer($offerId);
        }

        $products = $this->{$ptype};
        if ($full) {
            return $products;
        }

        switch ($ptype) {
            case 'transport':
                foreach ($products as $v) {
                    $produkty[] = array('id' => $v['hash'], 'nazwa' => $v['produkt_nazwa']);
                }
                return $produkty;
            default:
                $ptype === 'karty' ? $suffix = 'podtytul' : $suffix = 'nazwa';
                foreach ($products as $v) {
                    $produkty[] = array('id' => $v['identyfikator'], 'nazwa' => $v[$suffix][LANG]);
                }
                return $produkty;
        }
    }
}
<?php

use App\Libs\Models\PriceCalendar as PriceCalendarModel;
use MongoDB\BSON\UTCDateTime;


/**
 * Obsługa danych ofert
 *
 * <AUTHOR>
 */
class OfferData
{
    //  Obsługa bazy sql
    protected $sdb              = null;
    //  Obsługa bazy mongoDB
    protected $mdb              = null;
    //  Tablica operatorów do wykonania agregacji na bazie ofert w mongoDB
    protected $operators        = array();
    //  Tablica z ustawieniami do formatowanie produktów
    protected $format_settings  = array();
    protected $product_type     = array();
    protected $p_offer_type     = null;
    //  Tablice przechowujące dane poszczególnych elementów oferty
    protected $ticket           = array();
    protected $card             = array();
    protected $coupon           = array();
    protected $insurance        = array();
    protected $offer            = array();
    protected $product          = array();
    //  Tablica tymczasowa do zapisywania danych przy sprawdzaniu oferty
    public    $offers_check     = array();
    //  Dodatkowe zmienne
    protected $time             = null;
    protected $product_mgo_id   = null;
    protected $offer_id         = null;
    protected $parentid         = null;
    //  Określa ilość dni do pobrania ubezpieczenia
    protected $num_of_days      = null;
    //  Kontekst (front, partner)
    protected $context          = null;
    protected $m_time           = null;
    protected $date_from        = null;
    protected $date_to          = null;
    protected $transport_hash   = null;
    //  Dane wyjściowe
    public    $output           = array();
    //  Dane wejściowe
    public    $input            = array();
    //  Tablica błędów
    public    $errors           = array();

    
    public function __construct()
    {
        $this->sdb  = Database::activate();
        $this->mdb  = Mymongo::activate();
        $this->time = new DateTimeImmutable();
    }
    
    /**
     * Ustalanie tablicy operatorów
     * @param array|string $operators Tablica operatorów
     * @return boolean
     */
    public function setOperators( $operators )
    {
        if ( is_array( $operators ) )
        {
            $this->operators = $operators;
            return true;
        }
        
        $this->operators = array();
        
        switch ( $operators )
        {
            case 'order':
                if ( $this->parentid )
                {
                    $this->operators[]['$match'] = array(
                    'parentid'  => $this->parentid,
                    'status.id' => 5,
                    'current'   => 1
                    );
                    $this->operators[]['$project'] = array(
                        'parentid' => 1,
                        'osrodki'  => 1,
                        'nazwa'    => '$nazwa.' . LANG,
                    );
                }
                break;
                
            case 'offer_invoice':
                if ( $this->parentid )
                {
                    $this->operators[]['$match'] = array(
                    'parentid'  => $this->parentid,
                    'status.id' => 5,
                    'current'   => 1
                    );
                    $this->operators[]['$project'] = array(
                        'faktura_koszt' => 1,
                    );
                }
                break;
                
            case 'offer_transport_payment':
                if ( $this->parentid )
                {
                    $this->operators[]['$match'] = array(
                    'parentid'  => $this->parentid,
                    'status.id' => 5,
                    'current'   => 1
                    );
                    $this->operators[]['$project'] = array(
                        'przewoznicy' => 1,
                        'systemy_platnosci' => 1,
                    );
                }
                break;

            case 'offer_transport_data':
                if ( $this->parentid )
                {
                    $this->operators[]['$match'] = array(
                        'parentid'  => $this->parentid,
                        'status.id' => 5,
                        'current'   => 1
                    );
                    $this->operators[]['$unwind'] = '$przewoznicy';
                    $this->operators[]['$match']  = array('przewoznicy.hash' => $this->transport_hash);
                    $this->operators[]['$group'] = array(
                        '_id' => '$_id',
                        'przewoznicy'  => array( '$push'  => '$przewoznicy' )
                    );
                    $this->operators[]['$project'] = array(
                        'przewoznicy' => array(
                            '$map' => array(
                                'input' => '$przewoznicy',
                                'as'    => 'p',
                                'in' => array(
                                    'nazwa'         => '$$p.nazwa',
                                    'kurier_id'     => '$$p.kurier_id',
                                    'produkt_nazwa' => '$$p.produkt_nazwa',
                                    'hash'          => '$$p.hash',
                                ),
                            ),
                        ),
                    );
                }
                break;
                
            case 'karty':
                $m_time = new UTCDateTime( $this->time );

                $this->operators = array();

                if ( ! $this->parentid )
                {
                    $this->errors['setOperators'][] = 'Brak identyfikatora oferty';
                }

                $this->operators[]['$match'] = array(
                    'status.id' => 5,
                    'current'   => 1,
                    'parentid'  => $this->parentid,
                );
                $this->operators[]['$unwind'] = '$karty';
                $this->operators[]['$match'] = array(
                    'karty.sprzedaz_od' => array( '$lte' => $m_time ),
                    'karty.sprzedaz_do' => array( '$gte' => $m_time ),
                );

                if ( $this->product_mgo_id )
                {
                    $this->operators[]['$match'] = array(
                        'karty.identyfikator' => $this->product_mgo_id,
                    );  
                }

                if ( $this->context == 'front' )
                {
                    $this->operators[]['$match'] = array(
                        'karty.dostepna_na_stronie' => true,
                    );  
                }
                $this->operators[]['$group'] = array(
                    '_id' => '$_id',
                    'karty'  => array( '$push'  => '$karty' )
                );
                $this->operators[]['$project'] = array(
                    'karty' => array(
                        '$map' => array(
                            'input' => '$karty',
                            'as'    => 'karta',
                            'in' => array(
                                'identyfikator'    => '$$karta.identyfikator',
                                'typ_karty'        => '$$karta.typ_karty',
                                'nazwa'            => '$$karta.nazwa.' . LANG,
                                'podtytul'         => '$$karta.podtytul.' . LANG,
                                'note1'            => '$$karta.note1.' . LANG,
                                'note2'            => '$$karta.note2.' . LANG,
                                'pictures'         => '$$karta.pictures',
                                'ograniczone'      => '$$karta.ograniczone',
                                'zmiana_wizerunku' => '$$karta.zmiana_wizerunku',
                                'tylko_karta'      => '$$karta.tylko_karta',
                            ),
                        ),
                    ),
                );
                break;
                
            case 'virtual_products':
                $this->operators = array();
                $this->operators[]['$match'] = array(
                    'status.id'          => 5,
                    'current'            => 1,
                    'parentid'           => $this->parentid,
                    'partnerzy_sp.id'    => INSTALLATION,
                );
                $this->operators[]['$unwind'] = '$produkty_wirtualne';
                $this->operators[]['$match'] = array(
                    'produkty_wirtualne.sprzedaz_od' => array( '$lte' => $this->m_time ),
                    'produkty_wirtualne.sprzedaz_do' => array( '$gte' => $this->m_time ),
                    'produkty_wirtualne.dodatek' => 1,
                );
                $this->operators[]['$group'] = array(
                    '_id' => '$_id',
                    'produkty_wirtualne'  => array( '$push'  => '$produkty_wirtualne' )
                );
                $this->operators[]['$project'] = array(
                    'produkty_wirtualne' => array(
                        '$map' => array(
                            'input' => '$produkty_wirtualne',
                            'as'    => 'pv',
                            'in' => array(
                                'identyfikator' => '$$pv.identyfikator',
                                'nazwa'         => '$$pv.nazwa.' . LANG,
                                'podtytul'      => '$$pv.podtytul.' . LANG,
                                'note1'         => '$$pv.note1.' . LANG,
                                'note2'         => '$$pv.note2.' . LANG,
                                'obrazek'       => '$$pv.obrazek',
                            ),
                        ),
                    ),
                );
                break;
            
            case 'karta_single':
                if ( ! $this->parentid || ! $this->product_mgo_id )
                {
                    $this->errors['setOperators'][] = 'Brak identyfikatora oferty lub produktu';
                }
                
                $this->operators = array();
                $this->operators[]['$match'] = array(
                    'status.id' => 5,
                    'current'   => 1,
                    'parentid'  => $this->parentid,
                );
                $this->operators[]['$unwind'] = '$karty';
                $this->operators[]['$match'] = array(
                    'karty.identyfikator' => $this->product_mgo_id,
                );
                $this->operators[]['$project'] = array(
                    'karta' => array(
                        'identyfikator'    => '$karty.identyfikator',
                        'typ_karty'        => '$karty.typ_karty',
                        'nazwa'            => '$karty.nazwa.' . LANG,
                        'podtytul'         => '$karty.podtytul.' . LANG,
                        'pictures'         => '$karty.pictures',
                        'ograniczone'      => '$karty.ograniczone',
                        'zmiana_wizerunku' => '$karty.zmiana_wizerunku',
                        'tylko_karta'      => '$karty.tylko_karta',
                    ),
                );
                break;
                
            case 'product_name':
                $this->operators = array();
                $this->operators[0]['$match'] = array(
                    'status.id' => 5,
                    'current'   => 1,
                    'parentid'  => $this->parentid,
                );
                $this->operators[1]['$unwind'] = '$' . $this->p_offer_type;
                $this->operators[2]['$match'] = array(
                    $this->p_offer_type . '.identyfikator' => $this->product_mgo_id,
                );
                $this->operators[3]['$project'] = array(
                    $this->p_offer_type => array(
                        'nazwa'            => '$' . $this->p_offer_type . '.nazwa.' . LANG,
                        'podtytul'         => '$' . $this->p_offer_type . '.podtytul.' . LANG,
                    ),
                );
                
                if ( $this->p_offer_type == 'produkty_wirtualne' )
                {
                    $this->operators[3]['$project'][ $this->p_offer_type ]['note1']   = '$' . $this->p_offer_type . '.note1.' . LANG;
                    $this->operators[3]['$project'][ $this->p_offer_type ]['note2']   = '$' . $this->p_offer_type . '.note2.' . LANG;
                    $this->operators[3]['$project'][ $this->p_offer_type ]['obrazek'] = '$' . $this->p_offer_type . '.obrazek';
                }
                
                break;
                
            case 'ubezpieczenia':
                $m_time = new UTCDateTime( $this->time );
                
                $this->operators = array();
                
                if ( ! $this->parentid )
                {
                    $this->errors['setOperators'][] = 'Brak identyfikatora oferty';
                }
                
                $this->operators[]['$match'] = array(
                    'status.id' => 5,
                    'current'   => 1,
                    'parentid'  => $this->parentid,
                );
                $this->operators[]['$unwind'] = '$ubezpieczenia';
                $this->operators[]['$match'] = array(
                    'ubezpieczenia.sprzedaz_od' => array( '$lte' => $m_time ),
                    'ubezpieczenia.sprzedaz_do' => array( '$gte' => $m_time ),
                );
                
                if ( $this->product_mgo_id )
                {
                    $this->operators[]['$match'] = array(
                        'ubezpieczenia.identyfikator' => $this->product_mgo_id,
                    );
                }
                else if ( $this->num_of_days )
                {
                    $this->operators[]['$match'] = array(
                        'ubezpieczenia.czas_trwania' => $this->num_of_days,
                        '$or' => array( 
                            array( 'ubezpieczenia.standalone' => false ),
                            array( 'ubezpieczenia.standalone' => array( '$exists' => false ) ),
                        ),
                    );
                }
                
                if ( $this->context == 'front' )
                {
                    $this->operators[]['$match'] = array(
                        'ubezpieczenia.dostepna_na_stronie' => true,
                    );
                }
                
                $this->operators[]['$group'] = array(
                    '_id' => '$_id',
                    'ubezpieczenia'  => array( '$push'  => '$ubezpieczenia' )
                );
                $this->operators[]['$project'] = array(
                    'ubezpieczenia' => array(
                        '$map' => array(
                            'input' => '$ubezpieczenia',
                            'as'    => 'ins',
                            'in' => array(
                                'identyfikator' => '$$ins.identyfikator',
                                'czas_trwania'  => '$$ins.czas_trwania',
                                'nazwa'         => '$$ins.nazwa.' . LANG,
                                'podtytul'      => '$$ins.podtytul.' . LANG,
                                'note1'         => '$$ins.note1.' . LANG,
                                'note2'         => '$$ins.note2.' . LANG,
                                'ograniczone'   => '$$ins.ograniczone',
                                'sprzedaz_od'   => '$$ins.sprzedaz_od',
                                'sprzedaz_do'   => '$$ins.sprzedaz_do',
                                'pokaz_daty'    => '$$ins.pokaz_daty'
                            ),
                        ),
                    ),
                );
                
                break;
                
            case 'ubezpieczenia_conf':
                $m_time = new UTCDateTime( $this->time );
                $this->operators = array();
                $this->operators[]['$match'] = array(
                    'status.id' => 5,
                    'current'   => 1,
                    'partnerzy_sp.id' => INSTALLATION,
                );
                $this->operators[]['$unwind'] = '$ubezpieczenia';
                $this->operators[]['$match'] = array(
                    'ubezpieczenia.sprzedaz_od' => array( '$lte' => $m_time ),
                    'ubezpieczenia.sprzedaz_do' => array( '$gte' => $m_time ),
                    'ubezpieczenia.standalone' => true,
                );
                if ( $this->context == 'front' )
                {
                    $this->operators[]['$match'] = array(
                        'ubezpieczenia.dostepna_na_stronie' => true,
                    );  
                }
                $this->operators[]['$group'] = array(
                    '_id' => '$_id',
                    'parentid' => array( '$first' => '$parentid' ),
                    'ubezpieczenia'  => array( '$push'  => '$ubezpieczenia' )
                );
                $this->operators[]['$project'] = array(
                    '_id' => 0,
                    'ubezpieczenia' => array(
                        '$map' => array(
                            'input' => '$ubezpieczenia',
                            'as'    => 'ins',
                            'in' => array(
                                'identyfikator' => '$$ins.identyfikator',
                                'czas_trwania'  => '$$ins.czas_trwania',
                                'nazwa'         => '$$ins.nazwa.' . LANG,
                                'podtytul'      => '$$ins.podtytul.' . LANG,
                                'note1'         => '$$ins.note1.' . LANG,
                                'note2'         => '$$ins.note2.' . LANG,
                                'ograniczone'   => '$$ins.ograniczone',
                                'standalone'    => '$$ins.standalone',
                                'sprzedaz_od'   => '$$ins.sprzedaz_od',
                                'sprzedaz_do'   => '$$ins.sprzedaz_do',
                                'pokaz_daty'    => '$$ins.pokaz_daty',
                                'partner_id'    => '$$ins.partner_id',
                                'parentid'      => '$parentid',
                            ),
                        ),
                    ),
                );
                
                
                break;
                
            case 'ubezpieczenie_single':
                if ( ! $this->parentid || ! $this->product_mgo_id )
                {
                    $this->errors['setOperators'][] = 'Brak identyfikatora oferty lub produktu';
                }
                
                $this->operators = array();
                
                $this->operators[]['$match'] = array(
                    'status.id' => 5,
                    'current'   => 1,
                    'parentid'  => $this->parentid,
                );
                $this->operators[]['$unwind'] = '$ubezpieczenia';
                $this->operators[]['$match'] = array(
                    'ubezpieczenia.identyfikator' => $this->product_mgo_id,
                );
                $this->operators[]['$project'] = array(
                    'ubezpieczenie' => array(
                        'identyfikator' => '$ubezpieczenia.identyfikator',
                        'czas_trwania'  => '$ubezpieczenia.czas_trwania',
                        'nazwa'         => '$ubezpieczenia.nazwa.' . LANG,
                        'podtytul'      => '$ubezpieczenia.podtytul.' . LANG,
                        'note1'         => '$ubezpieczenia.note1.' . LANG,
                        'note2'         => '$ubezpieczenia.note2.' . LANG,
                        'ograniczone'   => '$ubezpieczenia.ograniczone',
                    ),
                );
                break;
                
            case 'karnet_single':
                if ( ! $this->parentid || ! $this->product_mgo_id )
                {
                    $this->errors['setOperators'][] = 'Brak identyfikatora oferty lub produktu';
                }
                
                $this->operators = array();
                
                $this->operators[]['$match'] = array(
                    'status.id' => 5,
                    'current'   => 1,
                    'parentid'  => $this->parentid,
                );
                $this->operators[]['$unwind'] = '$karnety';
                $this->operators[]['$match'] = array(
                    'karnety.identyfikator' => $this->product_mgo_id,
                );
                $this->operators[]['$project'] = array(
                    'karnet' => array(
                        'identyfikator'       => '$karnety.identyfikator',
                        'nazwa'               => '$karnety.nazwa.' . LANG,
                        'podtytul'            => '$karnety.podtytul.' . LANG,
                        'note1'               => '$karnety.note1.' . LANG,
                        'note2'               => '$karnety.note2.' . LANG,
                        'wariant_cenowy'      => '$karnety.wariant_cenowy',
                        'sprzedaz_od'         => '$karnety.sprzedaz_od',
                        'sprzedaz_do'         => '$karnety.sprzedaz_do',
                        'pokaz_daty'         =>  '$karnety.pokaz_daty',
                        'obowiazuje'          => '$karnety.obowiazuje',
                        'ubezpieczenie'       => '$karnety.ubezpieczenie',
                        'liczba_dni'          => '$karnety.liczba_dni',
                        'liczba_godzin'       => '$karnety.liczba_godzin',
                        'dostepna_na_stronie' => '$karnet.dostepna_na_stronie',
                        'ograniczone'         => '$karnety.ograniczone',
                        'gwarancja_zwrot'     => '$karnety.gwarancja_zwrot',
                        'osrodki'             => '$osrodki',
                    ),
                );
                break;
                
            case 'main_offer':
                $now = new DateTime();
                $this->operators[0]['$match'] = array(
                    'status.id'       => 5,
                    'current'         => 1,
                    'partnerzy_sp.id' => INSTALLATION,
                    'data_od' => [ '$lte' => new MongoDB\BSON\UTCDateTime($now)],
                    'data_do' => [ '$gte' => new MongoDB\BSON\UTCDateTime($now)],
                );

                if($this->parentid)
                {
                    $this->operators[0]['$match']['parentid'] = $this->parentid;
                }
                $this->operators[1]['$project'] = array(
                    '_id' => 0,
                    'parentid' => 1,
                    'kupon_rabatowy' => 1,
                    'nazwa' => '$nazwa.' . LANG,
                    'meta'=>1,
                    'karnet_nazwa' => '$karnet_nazwa',
                    'voucher_nazwa' => '$voucher_nazwa',
                    'karta_nazwa' => '$karta_nazwa',
                    'virtualp_nazwa' => '$virtualp_nazwa',
                    'karnety' => array(
                        '$map' => array(
                            'input' => '$karnety',
                            'as'    => 'karnet',
                            'in'    => array(
                                'sortowanie'          => '$$karnet.sortowanie',
                                'identyfikator'       => '$$karnet.identyfikator',
                                'nazwa'               => '$$karnet.nazwa.' . LANG,
                                'podtytul'            => '$$karnet.podtytul.' . LANG,
                                'wariant_cenowy'      => '$$karnet.wariant_cenowy',
                                'sprzedaz_od'         => '$$karnet.sprzedaz_od',
                                'sprzedaz_do'         => '$$karnet.sprzedaz_do',
                                'pokaz_daty'         =>  '$$karnet.pokaz_daty',
                                'obowiazuje'          => '$$karnet.obowiazuje',
                                'liczba_dni'          => '$$karnet.liczba_dni',
                                'liczba_godzin'       => '$$karnet.liczba_godzin',
                                'dostepna_na_stronie' => '$$karnet.dostepna_na_stronie',
                                'ograniczone'         => '$$karnet.ograniczone',
                                'rabaty'              => '$$karnet.rabaty',
                                'osrodki'             => '$osrodki',
                                'parentid'            => '$parentid',
                                'note1'                => '$$karnet.note1.'.LANG,
                                'note2'                => '$$karnet.note2.'.LANG,
                                'topup_group'           => '$$karnet.topup_group'
                            ),
                        ),
                    ),
                    'ubezpieczenia' => array(
                        '$map' => array(
                            'input' => '$ubezpieczenia',
                            'as'    => 'ins',
                            'in' => array(
                                'identyfikator'       => '$$ins.identyfikator',
                                'ubezpieczyciel'      => '$$ins.ubezpieczyciel',
                                'partner_id'          => '$$ins.partner_id',
                                'czas_trwania'        => '$$ins.czas_trwania',
                                'nazwa'               => '$$ins.nazwa.' . LANG,
                                'podtytul'            => '$$ins.podtytul.' . LANG,
                                'ograniczone'         => '$$ins.ograniczone',
                                'dostepna_na_stronie' => '$$ins.dostepna_na_stronie',
                                'sprzedaz_od'         => '$$ins.sprzedaz_od',
                                'sprzedaz_do'         => '$$ins.sprzedaz_do',
                                'pokaz_daty'         =>  '$$ins.pokaz_daty',
                                'standalone'          => '$$ins.standalone',
                                'osrodki'             => '$osrodki',
                                'parentid'            => '$parentid',
                                'note1'               => '$$ins.note1.'.LANG,
                                'note2'               => '$$ins.note2.'.LANG,
                            ),
                        ),
                    ),
                    'vouchery' => array(
                        '$map' => array(
                            'input' => '$vouchery',
                            'as'    => 'vou',
                            'in' => array(
                                'sortowanie'          => '$$vou.sortowanie',
                                'identyfikator'       => '$$vou.identyfikator',
                                'nazwa'               => '$$vou.nazwa.' . LANG,
                                'podtytul'            => '$$vou.podtytul.' . LANG,
                                'note1'               => '$$vou.note1.' . LANG,
                                'note2'               => '$$vou.note2.' . LANG,
                                'obrazek'             => '$$vou.obrazek',
                                'wariant_cenowy'      => '$$vou.wariant_cenowy',
                                'ograniczone'         => '$$vou.ograniczone',
                                'dostepna_na_stronie' => '$$vou.dostepna_na_stronie',
                                'sprzedaz_od'         => '$$vou.sprzedaz_od',
                                'sprzedaz_do'         => '$$vou.sprzedaz_do',
                                'pokaz_daty'         =>  '$$vou.pokaz_daty',
                                'rabaty'              => '$$vou.rabaty',
                                'attractions'             => '$$vou.attractions',
                                'attraction_base_promo_value' => '$$vou.attraction_base_promo_value',
                                'osrodki'             => '$osrodki',
                                'parentid'            => '$parentid',
                            ),
                        ),
                    ),
                    'karty' => array(
                        '$map' => array(
                            'input' => '$karty',
                            'as'    => 'karta',
                            'in' => array(
                                'sortowanie'          => '$$karta.sortowanie',
                                'identyfikator'       => '$$karta.identyfikator',
                                'typ_karty'           => '$$karta.typ_karty',
                                'nazwa'               => '$$karta.nazwa.' . LANG,
                                'podtytul'            => '$$karta.podtytul.' . LANG,
                                'note1'               => '$$karta.note1.' . LANG,
                                'note2'               => '$$karta.note2.' . LANG,
                                'ograniczone'         => '$$karta.ograniczone',
                                'zmiana_wizerunku'    => '$$karta.zmiana_wizerunku',
                                'dostepna_na_stronie' => '$$karta.dostepna_na_stronie',
                                'sprzedaz_od'         => '$$karta.sprzedaz_od',
                                'sprzedaz_do'         => '$$karta.sprzedaz_do',
                                'pokaz_daty'         =>  '$$karta.pokaz_daty',
                                'tylko_karta'         => '$$karta.tylko_karta',
                                'osrodki'             => '$osrodki',
                                'parentid'            => '$parentid',
                            ),
                        ),
                    ),
                    'produkty_wirtualne' => array(
                        '$map' => array(
                            'input' => '$produkty_wirtualne',
                            'as'    => 'pv',
                            'in' => array(
                                'identyfikator'       => '$$pv.identyfikator',
                                'nazwa'               => '$$pv.nazwa.' . LANG,
                                'podtytul'            => '$$pv.podtytul.' . LANG,
                                'note1'               => '$$pv.note1.' . LANG,
                                'note2'               => '$$pv.note2.' . LANG,
                                'obrazek'             => '$$pv.obrazek',
                                'dostepna_na_stronie' => '$$pv.dostepna_na_stronie',
                                'sprzedaz_od'         => '$$pv.sprzedaz_od',
                                'sprzedaz_do'         => '$$pv.sprzedaz_do',
                                'pokaz_daty'          => '$$pv.pokaz_daty',
                                'osrodki'             => '$osrodki',
                                'parentid'            => '$parentid',
                            ),
                        ),
                    ),
                );
                break;
            
            case 'partner_offer':
                $this->operators[]['$match'] = array(
                    'status.id'       => 5,
                    'current'         => 1,
                    'karnety'         => array( '$exists' => 1 ),
                    'partnerzy_sp.id' => INSTALLATION,
                );
                $this->operators[]['$unwind'] = '$karnety';
                $this->operators[]['$match'] = array(
                    'karnety.sprzedaz_od' => array( '$lte' => $this->m_time ),
                    'karnety.sprzedaz_do' => array( '$gte' => $this->m_time ),
                );

                if ( $this->date_from )
                {
                    $this->operators[]['$match']['karnety.obowiazuje.data_od']['$lte'] = new UTCDateTime( strtotime( $this->date_from ) * 1000);
                }

                if ( $this->date_to )
                {
                    $this->operators[]['$match']['karnety.obowiazuje.data_do']['$gte'] = new UTCDateTime( strtotime( $this->date_to ) *1000);
                }
                
                $this->operators[]['$project'] = array(
                    'parentid' => 1,
                    'osrodki'  => 1,
                    'karnety'  => array(
                        'sprzedaz_od'    => 1,
                        'sprzedaz_do'    => 1,
                        'obowiazuje'     => 1,
                        'wariant_cenowy' => 1,
                        'identyfikator'  => 1,
                        'ograniczone'    => 1,
                        'podtytul'       => '$karnety.podtytul.' . LANG,
                        'nazwa'          => '$karnety.nazwa.' . LANG,
                        'note1'          => '$karnety.note1.' . LANG,
                        'note2'          => '$karnety.note2.' . LANG,
                        'parentid'       => '$parentid',
                        'osrodki'        => '$osrodki',
                        'nosnik_karta'         => '$karnety.nosnik.karta',
                        'nosnik_pdf'         => '$karnety.nosnik.voucher',
                    )
                );
                $this->operators[]['$group'] = array(
                    '_id'      => '$_id',
                    'osrodki'  => array( '$first' => '$osrodki' ),
                    'parentid' => array( '$first' => '$parentid' ),
                    'karnety'  => array( '$push'  => '$karnety' )
                );
                $this->operators[]['$project'] = array(
                    'parentid' => 1,
                    'osrodki'  => 1,
                    'karnety'  => array(
                        'identyfikator'  => 1,
                        'sprzedaz_od'    => 1,
                        'sprzedaz_do'    => 1,
                        'obowiazuje'     => 1,
                        'ograniczone'    => 1,
                        'wariant_cenowy' => 1,
                        'podtytul'       => 1,
                        'nazwa'          => 1,
                        'note1'          => 1,
                        'note2'          => 1,
                        'parentid'       => 1,
                        'osrodki'        => 1,
                        'nosnik_karta'   => 1,
                        'nosnik_pdf'     => 1,
                    )
                );
                break;
                
            case 'partner_offer_wvoucher':
                $this->operators[]['$match'] = [
                    'status.id'       => 5,
                    'current'         => 1,
                    'vouchery'         => ['$exists' => 1],
                    'partnerzy_sp.id' => INSTALLATION,
                ];
                $this->operators[]['$unwind'] = '$vouchery';
                $this->operators[]['$match'] = [
                    'vouchery.sprzedaz_od' => ['$lte' => $this->m_time],
                    'vouchery.sprzedaz_do' => ['$gte' => $this->m_time],
                    'vouchery.grupa_produktowa'=> ['$eq' => 0]
                ];

                $this->operators[]['$project'] = [
                    'parentid' => 1,
                    'osrodki'  => 1,
                    'vouchery' => [
                                'identyfikator'       => 1,
                                'nazwa'               => '$vouchery.nazwa.' . LANG,
                                'podtytul'            => '$vouchery.podtytul.' . LANG,
                                'wariant_cenowy'      => '$vouchery.wariant_cenowy',
                                'ograniczone'         => 1,
                                'rabaty'              => '$vouchery.rabaty',
                                'dostepna_na_stronie' => 1,
                                'sprzedaz_od'         => 1,
                                'sprzedaz_do'         => 1,
                                'note1'         => '$vouchery.note1.'.LANG,
                                'note2'         => '$vouchery.note2.'.LANG,
                                'attractions'             => '$vouchery.attractions',
                                'attraction_base_promo_value' => '$vouchery.attraction_base_promo_value',
                                'grupa_produktowa' =>1,
                                'osrodki'=>'$osrodki',
                    ],
                ];
                
                $this->operators[]['$group'] = array(
                    '_id'      => '$_id',
                    'osrodki'  => ['$first' => '$osrodki'],
                    'parentid' => ['$first' => '$parentid'],
                    'vouchery'  => ['$push'  => '$vouchery']
                );
                break;
            
            case 'partner_offer_gvoucher':
                $this->operators[]['$match'] = array(
                    'status.id'       => 5,
                    'current'         => 1,
                    'vouchery'         => array( '$exists' => 1 ),
                    'partnerzy_sp.id' => INSTALLATION,
                );
                $this->operators[]['$unwind'] = '$vouchery';
                $this->operators[]['$match'] = array(
                    'vouchery.sprzedaz_od' => array( '$lte' => $this->m_time ),
                    'vouchery.sprzedaz_do' => array( '$gte' => $this->m_time ),
                    'vouchery.grupa_produktowa'=> array('$ne' => 0)
                );

                $this->operators[]['$project'] = array(
                    'parentid' => 1,
                    'osrodki'  => 1,
                    'vouchery' => [
                                'identyfikator'       => 1,
                                'nazwa'               => '$vouchery.nazwa.' . LANG,
                                'podtytul'            => '$vouchery.podtytul.' . LANG,
                                'wariant_cenowy'      => '$vouchery.wariant_cenowy',
                                'ograniczone'         => 1,
                                'rabaty'              => '$vouchery.rabaty',
                                'dostepna_na_stronie' => 1,
                                'klient_indywidualny' => 1,
                                'sprzedaz_od'         => 1,
                                'sprzedaz_do'         => 1,
                                'note1'         => '$vouchery.note1.'.LANG,
                                'note2'         => '$vouchery.note2.'.LANG,
                                'attractions'             => '$vouchery.attractions',
                                'attraction_base_promo_value' => '$vouchery.attraction_base_promo_value',
                                'grupa_produktowa' =>1,
                                'osrodki'=>'$osrodki',
                    ],
                );
                
                $this->operators[]['$group'] = array(
                    '_id'      => '$_id',
                    'osrodki'  => array( '$first' => '$osrodki' ),
                    'parentid' => array( '$first' => '$parentid' ),
                    'vouchery'  => array( '$push'  => '$vouchery' )
                );
                break;
            
            case 'partner_offer_ubezpieczenia':
                $this->operators[]['$match'] = array(
                    'status.id'       => 5,
                    'current'         => 1,
                    'ubezpieczenia'         => array( '$exists' => 1 ),
                    'partnerzy_sp.id' => INSTALLATION,
                );
                $this->operators[]['$unwind'] = '$ubezpieczenia';
                $this->operators[]['$match'] = array(
                    'ubezpieczenia.sprzedaz_od' => array( '$lte' => $this->m_time ),
                    'ubezpieczenia.sprzedaz_do' => array( '$gte' => $this->m_time ),
                    'ubezpieczenia.standalone' => true,
                );
                
                $this->operators[]['$project'] = array(
                    'parentid' => 1,
                    'osrodki'  => 1,
                    'ubezpieczenia' => array(
                                'identyfikator'       => 1,
                                'nazwa'               => '$ubezpieczenia.nazwa.' . LANG,
                                'podtytul'            => '$ubezpieczenia.podtytul.' . LANG,
                                'wariant_cenowy'      => '$ubezpieczenia.wariant_cenowy',
                                'ograniczone'         => 1,
                                'rabaty'              => '$ubezpieczenia.rabaty',
                                'dostepna_na_stronie' => 1,
                                'sprzedaz_od'         => 1,
                                'sprzedaz_do'         => 1,
                                'note1'         => '$ubezpieczenia.note1.'.LANG,
                                'note2'         => '$ubezpieczenia.note2.'.LANG,
                                'partner_id' =>1,
                                'parentid'=>'$parentid'
                                )
                    );

                
                $this->operators[]['$group'] = array(
                    '_id'      => '$_id',
                    'osrodki'  => array( '$first' => '$osrodki' ),
                    'parentid' => array( '$first' => '$parentid' ),
                    'ubezpieczenia'  => array( '$push'  => '$ubezpieczenia' )
                );
                $this->operators[]['$project'] = array(
                    'parentid' => 1,
                    'partnerid' => 1,
                    'osrodki'  => 1,
                    'ubezpieczenia' => array(
                                'identyfikator'       => 1,
                                'nazwa'               => 1,
                                'podtytul'            => 1,
                                'wariant_cenowy'      => 1,
                                'ograniczone'         => 1,
                                'rabaty'              => 1,
                                'dostepna_na_stronie' => 1,
                                'sprzedaz_od'         => 1,
                                'sprzedaz_do'         => 1,
                                'note1'         => 1,
                                'note2'         => 1,
                                'parentid'     => 1,
                                'partner_id' =>1    
                    )
//                    'ubezpieczenia' =>array(
//                        '$map' => array(
//                            'input' => '$ubezpieczenia',
//                            'as'    => 'ubez',
//                            'in' => array(
//                                'identyfikator'       => '$$ubez.identyfikator',
//                                'nazwa'               => '$$ubez.nazwa.' . LANG,
//                                'podtytul'            => '$$ubez.podtytul.' . LANG,
//                                'wariant_cenowy'      => '$$ubez.wariant_cenowy',
//                                'ograniczone'         => '$$ubez.ograniczone',
//                                'rabaty'              => '$$ubez.rabaty',
//                                'dostepna_na_stronie' => '$$ubez.dostepna_na_stronie',
//                                'sprzedaz_od'         => '$$ubez.sprzedaz_od',
//                                'sprzedaz_do'         => '$$ubez.sprzedaz_do',
//                                'note1'         => '$$ubez.note1.'.LANG,
//                                'note2'         => '$$ubez.note2.'.LANG,
//                                'parentid' =>'$parentid',
//                                'partnerid'=>'$partnerid'
//                            ),
//                        ),
//                    ),
                );
                break;
            
            case 'offer_tickets_vouchers':
                $this->operators[]['$match'] = array(
                    'status.id'       => 5,
                    'current'         => 1,
                    'partnerzy_sp.id' => INSTALLATION,
                );
                $this->operators[]['$project'] = array(
                    '_id'      => 0,
                    'partnerzy_sp' => 1,
                    'nazwa' => '$nazwa.' . LANG,
                    'karnety' => array(
                        '$map' => array(
                            'input' => '$karnety',
                            'as'    => 'karnet',
                            'in'    => array(
                                'identyfikator'       => '$$karnet.identyfikator',
                                'nazwa'               => '$$karnet.nazwa.' . LANG,
                                'podtytul'            => '$$karnet.podtytul.' . LANG,
                                'wariant_cenowy'      => '$$karnet.wariant_cenowy',
                                'sprzedaz_od'         => '$$karnet.sprzedaz_od',
                                'sprzedaz_do'         => '$$karnet.sprzedaz_do',
                                'obowiazuje'          => '$$karnet.obowiazuje',
                                'liczba_dni'          => '$$karnet.liczba_dni',
                                'liczba_godzin'       => '$$karnet.liczba_godzin',
                                'dostepna_na_stronie' => '$$karnet.dostepna_na_stronie',
                                'ograniczone'         => '$$karnet.ograniczone',
                                'rabaty'              => '$$karnet.rabaty',
                            ),
                        ),
                    ),
                    'vouchery' => array(
                        '$map' => array(
                            'input' => '$vouchery',
                            'as'    => 'vou',
                            'in' => array(
                                'identyfikator'       => '$$vou.identyfikator',
                                'nazwa'               => '$$vou.nazwa.' . LANG,
                                'podtytul'            => '$$vou.podtytul.' . LANG,
                                'wariant_cenowy'      => '$$vou.wariant_cenowy',
                                'ograniczone'         => '$$vou.ograniczone',
                                'rabaty'              => '$$vou.rabaty',
                                'dostepna_na_stronie' => '$$vou.dostepna_na_stronie',
                                'sprzedaz_od'         => '$$vou.sprzedaz_od',
                                'sprzedaz_do'         => '$$vou.sprzedaz_do',
                                'pokaz_daty'         => '$$vou.pokaz_daty',
                            ),
                        ),
                    ),
                );
                break;
            
            case 'check_virtual_products':
                $this->operators[]['$match'] = array(
                    'status.id'          => 5,
                    'current'            => 1,
                    'produkty_wirtualne' => array( '$exists' => 1 ),
                    'parentid'           => $this->parentid,
                    'partnerzy_sp.id'    => INSTALLATION,
                );
                $this->operators[]['$unwind'] = '$produkty_wirtualne';
                $this->operators[]['$match'] = array(
                    'produkty_wirtualne.sprzedaz_od' => array( '$lte' => $this->m_time ),
                    'produkty_wirtualne.sprzedaz_do' => array( '$gte' => $this->m_time ),
                    'produkty_wirtualne.dodatek' => 1,
                );
                $this->operators[]['$project'] = array(
                    'produkty_wirtualne'  => array(
                        'identyfikator'  => 1,
                        'ograniczone'    => 1,
                        'sprzedaz_od'    => 1,
                        'sprzedaz_do'    => 1,
                    )
                );
                $this->operators[]['$group'] = array(
                    '_id' => '$_id',
                    'produkty_wirtualne'  => array( '$push'  => '$produkty_wirtualne' )
                );
                $this->operators[]['$project'] = array(
                    'parentid' => 1,
                    'produkty_wirtualne'  => array(
                        'identyfikator'  => 1,
                        'ograniczone'    => 1,
                        'sprzedaz_od'    => 1,
                        'sprzedaz_do'    => 1,
                    )
                );
                
                break;
        }
        
        if ( $this->operators )
        {
            return true;
        }
        return false;
    }
    
    /**
     * Resetowanie operatorów
     */
    public function ResetOperators(){
        $this->operators = array();
    }
    
    /**
     * Ustalanie danych wyjściowych na pojedyńczą tablicę
     * @return boolean
     */
    public function setSingle()
    {
        if ( $this->output[0] )
        {
            $this->output = $this->output[0];
        }
        return true;
    }
    
    public function setMTime()
    {
        $this->m_time = new UTCDateTime(new DateTime());
        return true;
    }
    
    public function setCard( $card )
    {
        if ( is_array( $card ) )
        {
            $this->card = $card;
            return true;
        }
        else
        {
            $this->errors['setCard'][] = App::_Lang('Dane karty muszą być tablicą','Oferty');
            return false;
        }
    }

    /**
     * Ustalanie danych karnetu
     * @param type $ticket
     * @return boolean
     */
    public function setTicket( $ticket )
    {
        if ( is_array( $ticket ) )
        {
            $this->ticket = $ticket;
            return true;
        }
        else
        {
            $this->errors['setTicket'][] = App::_Lang('Dane karnetu muszą być tablicą','Oferty');
            return false;
        }
    }
    
    public function setProductBsid( $bsid )
    {
        $this->product['bsid'] = $bsid;
        return true;
    }
    
    public function addProductCoupon( $coupon )
    {
        $this->format_settings['add_coupon'] = true;
        $this->coupon = $coupon;
        return true;
    }
    
    public function setProduct( $product )
    {
        $this->product = $product;
        return true;
    }
    
    public function setNumOfDays( $num_of_days )
    {
        $this->num_of_days = $num_of_days;
        return true;
    }
    
    /**
     * Deklarowanie identyfikatora produktu
     * @param string $mgo_id Identyfikator produktu
     * @return boolean
     */
    public function setProductMgoId( $mgo_id )
    {
        $this->product_mgo_id = $mgo_id;
        return true;
    }
    
    public function setOutput( $output )
    {
        $this->output = $output;
        return true;
    }
    
    public function setInput( $input )
    {
        $this->input = $input;
        return true;
    }
    
    public function rOutput()
    {
        return $this->output;
    }

    public function setTransportHash($hash)
    {
        $this->transport_hash = $hash;
        return true;
    }
    
    /**
     * Ustalanie aktualnych cen dla wszystkich produktów
     * @return boolean
     */
    public function addProductPrice()
    {
        $this->format_settings['add_current_price'] = true;
        return true;
    }
    
    public function addProductPriceDisp()
    {
        $this->format_settings['add_current_price_disp'] = true;
        return true;
    }
    
    public function addProductDates( $format = 'Y-m-d' )
    {
        $this->format_settings['add_dates'] = $format;
        return true;
    }
    
    /**
     * Dodawanie informacji o zestawach zawierających produkt
     * @return boolean
     */
    public function addProductPacks( $item_list = false )
    {
        $this->format_settings['add_packs'] = true;
        return true;
    }
    
    public function addProductHasAddons()
    {
        $this->format_settings['add_product_has_addons'] = true;
        return true;
    }
    
    public function addProductSeasonDate()
    {
        $this->format_settings['add_season_date'] = true;
        return true;
    }
    
    /**
     * Ustalanie minimalnej ceny dla wszystkich produktów
     * @return boolean
     */
    public function addProductMinPrice()
    {
        $this->format_settings['add_min_price'] = true;
        return true;
    }
    
    /**
     * Dodawanie identyfikatora oferty do wszystkich produktów
     * @return boolean
     */
    public function addProductOfferId()
    {
        $this->format_settings['add_offer_id'] = true;
        return true;
    }
    
    public function setProductAvailable( $to_sell = true, $on_page = true )
    {
        $this->format_settings['set_available']['counter'] = true;
        $this->format_settings['set_available']['offline'] = true;
        
        if ( $to_sell )
        {
            $this->format_settings['set_available']['to_sell'] = true;
        }
        
        if ( $on_page )
        {
            $this->format_settings['set_available']['on_page'] = true;
        }
        return true;
    }
    
    public function addProductPromoEndDate()
    {
        $this->format_settings['add_promo_end_date'] = true;
        return true;
    }
    
    public function setProductResortsString()
    {
        $this->format_settings['set_resorts_string'] = true;
        return true;
    }
    
    public function setSingleCardsOnly()
    {
        $this->format_settings['set_single_cards_only'] = true;
        return true;
    }
    
    public function setInsurancesStandAloneOnly()
    {
        $this->format_settings['set_insurances_stand_alone_only'] = true;
        return true;
    }
    
    public function setProductDateFrom( $date_from )
    {
        $this->date_from = $date_from;
        return true;
    }
    
    public function setProductDateTo( $date_to )
    {
        $this->date_to = $date_to;
        return true;
    }
    
    ////////////////////////////////////////////////////////////////////////////////                    
    //  Funkcje ustalające parametry offerty
    ////////////////////////////////////////////////////////////////////////////////
    
    public function setContext( $context = 'front' )
    {
        $contexts = array( 'front', 'partner' );
        if ( in_array( $context, $contexts ) )
        {
            $this->context = $context;
            return true;
        }
        else
        {
            $this->errors['setContext'][] = App::_Lang('Nieprawidłowy kontekst','Oferty');
            return false;
        }
    }
    
    /**
     * Deklarowanie identyfikatora oferty
     * @param type $offer_id
     * @return boolean
     */
    public function setOfferId( $offer_id )
    {
        $this->offer_id = $offer_id;
        return true;
    }
    
    public function setParentId($parentid)
    {
        try {
            $_id = new \MongoDB\BSON\ObjectId($parentid);
            $this->parentid = $parentid;
            return true;
        } catch (\Exception $e) {
            return false;
        }
//        if(MongoId::IsValid($parentid))
//        {
//            $this->parentid = $parentid;
//            return true;
//        }
//        else
//        {
//            return false;
//        }
    }
    
    public function setProductType( $product_type )
    {
        $this->product_type = $product_type;
        return true;
    }
    
    public function setPOfferType( $p_offer_type )
    {
        $this->p_offer_type = $p_offer_type;
        return true;
    }
    
    /**
     * Dodawanie do oferty identyfikatora w formie string
     * @return boolean
     */
    public function addOfferId()
    {
        $this->format_settings['add_offer_id'] = true;
        return true;
    }
    
    /**
     * Filtrowanie ofert z karnetami
     * @return boolean
     */
    public function offerHasTickets()
    {
        $this->format_settings['has_karnety'] = true;
        return true;
    }
    
    /**
     * Przenoszenie wszystkich produktów bezpośrednio do tablicy z danymi wyjściowymi
     * @return boolean
     */
    public function moveProductsUp()
    {
        $this->format_settings['move_products_up'] = true;
        return true;
    }
    
    /**
     * Pobieranie danych oferty/ofert z bazy mongo
     * @return boolean
     */
    public function getOffer()
    {
        if ( ! empty( $this->operators ) )
        {
            $this->output = $this->mdb->Aggregate( $this->operators );
            if (DEBUG) {
                @file_put_contents('_offer_.dat',var_export($this->output,true).PHP_EOL,FILE_APPEND);
                @file_put_contents('_opps_.dat',var_export($this->operators,true).PHP_EOL,FILE_APPEND);
            }
            return true;
        }
    }
    
    ////////////////////////////////////////////////////////////////////////////////                    
    //  Funkcje formatujące dane wyjściowe
    ////////////////////////////////////////////////////////////////////////////////
    
    /**
     * Formatowanie danych oferty na podstawie ustalonych parametrów
     * @return boolean
     */
    public function format()
    {
        $this->result = false;
        $p_types = array( 'karnety', 'karty', 'vouchery', 'ubezpieczenia', 'produkty_wirtualne' );
        if ( $this->output )
        {
            foreach ( $this->output as $offer_key => &$offer )
            {
                if ( ! empty ( $this->format_settings ) )
                {
                    $this->offer = $offer;
                    
                    if ( $this->formatOffer() )
                    {
                       $offer = $this->offer; 
                    }
                }
                elseif ( $offer['_id']['$oid'] )
                {
                    $this->offer_id = $offer['_id']['$oid'];
                }


                foreach ( $p_types as $p_type )
                {
                    if ( ! empty ( $this->format_settings ) && ! empty ( $offer[ $p_type ] ) )
                    {
                        foreach ( $offer[ $p_type ] as $p_key => &$p )
                        {
                            $this->product = $p;
                            $this->product_type = $p_type;

                            if ( $this->formatProduct() && ! empty ( $this->product ) )
                            {
                               $p = $this->product;
                            }
                            else
                            {
                                unset( $this->output[ $offer_key ][ $p_type ][ $p_key ] );

                                if ( count( $this->output[ $offer_key ][ $p_type ] ) < 1 )
                                {
                                    unset( $this->output[ $offer_key ][ $p_type ] );
                                }

                                if (  $this->format_settings[ 'has_' . $p_type ] && ! $this->output[ $offer_key ][ $p_type ] )
                                {
                                    unset( $this->output[ $offer_key ] );
                                    continue 2;
                                }
                            }
                        }

                        //  Przesuwanie produktów bezbośrednio do tablicy output
                        if ( $this->format_settings['move_products_up'] && $this->output[ $offer_key ][ $p_type ] )
                        {
                            if ( $this->output[ $p_type ] )
                            {
                                $this->output[ $p_type ] = array_merge( $this->output[ $p_type ], $offer[ $p_type ] );
                            }
                            else
                            {
                                $this->output[ $p_type ] = $offer[ $p_type ];
                            }

                            unset( $this->output[ $offer_key ][ $p_type ] );
                        }
                        else
                        {
                            $this->errors['format'][] = App::_Lang('Brak produktów','Oferty') . ' ' . $p_type . ' ' . App::_Lang('do przesunięcia','Oferty');
                        }
                    }
                }
            }
        }
        else
        {
            return false;
        }
        $this->result = true;
        return true;    
    }
    
    public function formatProducts()
    {
        $p_types = array( 'karnety', 'karty', 'vouchery', 'ubezpieczenia', 'produkty_wirtualne' );
        if ( $this->output )
        {
            foreach ( $p_types as $p_type )
            {
                if ( ! empty ( $this->format_settings ) && ! empty ( $this->output[ $p_type ] ) )
                {
                    foreach ( $this->output[ $p_type ] as $p_key => &$p )
                    {
                        $this->product      = $p;
                        $this->product_type = $p_type;
                        
                        if ( $this->formatProduct() && ! empty ( $this->product ) )
                        {
                           $p = $this->product;
                        }
                        else
                        {
                            unset( $this->output[ $p_type ][ $p_key ] );

                            if ( count( $this->output[ $p_type ] ) < 1 )
                            {
                                unset( $this->output[ $p_type ] );
                            }
                        }
                    }
                }
            }
        }
        else
        {
            return false;
        }
        
        $this->result = true;
        return true;    
    }
    
    public function prepareProductGroups(){
        $grupy = array();
        if(!empty($this->output)){
            foreach($this->output['vouchery'] ?? [] as $k=>$v){
                $grupy[$v['grupa_produktowa']]['vouchery'][] = $v;
            }
        }
        $this->output['grupy'] = $grupy;
        unset($this->output['vouchery']);
    }


    public function formatProduct( $set_output = false )
    {
        if ( $this->product )
        {
            if ( $this->format_settings['add_offer_id'] && $this->offer_id )
            {
                $this->product['offer_id'] = $this->offer_id;
            }

            if ( $this->format_settings['set_available'] )
            {
                if ( ! $this->formatAvailable() )
                {
                    return false;
                }
            }
            
            if ( $this->format_settings['add_dates'] )
            {
                if ( ! $this->formatDates() )
                {
                    return false;
                }
            }
            
            if ( $this->format_settings['add_season_date'] )
            {
                if ( ! $this->formatAddSeasonDate() )
                {
                    return false;
                }
            }

            if ( $this->format_settings['set_resorts_string'] )
            {
                if ( ! $this->formatResortString() )
                {
                    return false;
                }
            }

            if ( $this->format_settings['add_current_price'] )
            {
                if ( ! $this->formatCurrentPrice() )
                {
                    return false;
                }
            }

            if ( $this->format_settings['add_packs'] )
            {
                if ( ! $this->formatPacks() )
                {
                    return false;
                }
            }

            if ( $this->format_settings['add_coupon'] )
            {
                if ( ! $this->formatAddCoupon() )
                {
                    return false;
                }
            }

            if ( $this->format_settings['set_single_cards_only'] )
            {
                if ( ! $this->formatSetSingleCardsOnly() )
                {
                    return false;
                }
            }

            if ( $this->format_settings['set_insurances_stand_alone_only'] )
            {
                if ( ! $this->formatSetInsuranceStandAloneOnly() )
                {
                    return false;
                }
            }

            if ( $set_output )
            {
                $this->result = true;
                $this->output = $this->product;
            }
            
            return true;
        }
        
        return false;
    }
    
    private function formatAddSeasonDate()
    {
        
        if ( is_array( $this->product['obowiazuje']['data_od'] ) && is_array( $this->product['obowiazuje']['data_do'] )  )
        {
            $data_od = $this->product['obowiazuje']['data_od']['sec'];
            $data_do = $this->product['obowiazuje']['data_do']['sec'];
        }
        else if ( $this->product['obowiazuje']['data_od'] && $this->product['obowiazuje']['data_do'] )
        {
            $data_od = strtotime( $this->product['obowiazuje']['data_od'] );
            $data_do = strtotime( $this->product['obowiazuje']['data_do'] );
        }
        else
        {
            return false;
        }
        
        $year_from = date( 'Y', $data_od );
        $year_to = date( 'Y', $data_do );
        
        if ( $year_from == $year_to )
        {
            $this->product['obowiazuje']['sezon'] = $year_from;
        }
        else
        {
            $this->product['obowiazuje']['sezon'] = $year_from . '/' . $year_to;
        }
        return true;
    }
    
    private function formatAddCoupon()
    {
        foreach ( $this->coupon as $coupon )
        {
            if ( $this->product['cena'] - $coupon['wartosc_jedn_kuponu'] < 1 )
            {
                $this->product['cena'] = 1;
            }
            else
            {
                $this->product['cena'] = Tools::CalcNf( $this->product['cena'], $coupon['wartosc_jedn_kuponu'], 'sub' );
            }
        }
        return true;
    }
    
    private function formatPacks()
    {
        
        if ( $this->product['bsid'] )
        {
            $pack_data = Kupon::getProductPackData( $this->product['identyfikator'], $this->product['bsid'], $this->product['identyfikator'] );
        }
        else
        {
            $pack_data = Kupon::getProductPackData( $this->product['identyfikator'] );
        }

        $this->product['pack_data'] = $pack_data ? $pack_data : false;
        
        //  Dodanie informacji o cenach warianu
        if ( $this->product['pack_data'] )
        {
            foreach ( $this->product['pack_data'] as $pack_key => &$pack_data )
            {
                foreach ( $pack_data['produkty'] as &$produkt )
                {
                    if ( $produkt['identyfikator'] == $this->product['identyfikator'] && $produkt['wartosc_jedn_kuponu'] )
                    {
                        foreach ( $this->product['wariant_cenowy'] as $variant ) 
                        {
                            $price_data = array();
                            if ( $variant['cena'] - $produkt['wartosc_jedn_kuponu'] < 1 )
                            {
                                $price_data['cena'] = 1;
                            }
                            else
                            {
                                $price_data['cena'] = Tools::CalcNf( $variant['cena'], $produkt['wartosc_jedn_kuponu'], 'sub' );
                            }

                            $produkt['wariant_cenowy'][ $variant['bsid'] ] = $price_data;
                        }
                    }
                }
            }
        }
        
        return true;
    }
    
    private function formatCurrentPrice()
    {
        switch ( $this->product_type )
        {
            default: return false;
            case 'karnety':
            case 'vouchery':
                if ( $this->product['rabaty']['procentowe'] )
                {
                    foreach ( $this->product['rabaty']['procentowe'] as $rabat )
                    {
                        if ( $rabat['data_od']['sec'] < $this->time->getTimestamp() && $rabat['data_do']['sec'] > $this->time->getTimestamp() )
                        {
                            $this->product['rabat'] = intval( $rabat['rabat'] * 100 );
                            // $this->product['rabat_ends'] = date( 'Y-m-d', $rabat['data_do']['sec'] );
                            // add hours minutes and secods to the end date
                            $this->product['rabat_ends'] = date( 'Y-m-d H:i:s',  $rabat['data_do']['sec']  );
                            break;
                        }
                    }
                    unset( $this->product['rabaty'] );
                }

                if ( $this->format_settings['add_min_price'] )
                {
                    $promo = Userproducts::getMaxPromo( $this->product['identyfikator'] );
                }

                $price_data = array();
                $sql = 'SELECT cena, id_wariantu FROM ' . TABLE_PREFIX . 'cennik ';
                $sql.= 'WHERE id_produktu = "' . $this->product['identyfikator'] . '" ';

                if ( $this->product['bsid'] )
                {
                    $sql.= 'AND id_wariantu = "' . $this->product['bsid'] . '" ';
                }

                $result = $this->sdb->query( $sql );

                while( $row = $this->sdb->fetch_row() )
                {
                    $price_data[ $row['id_wariantu'] ] = $row['cena'];
                }

                if ( $price_data )
                {
                    foreach ( $this->product['wariant_cenowy'] as $w_key => &$wariant_cenowy )
                    {
                        $base_price_for_omnibus = $wariant_cenowy['cena']['brutto'];
                        $omnibus_price = Tools::getLastLowestPrice($this->offer['parentid'], $this->product['identyfikator'], $wariant_cenowy['bsid'],$base_price_for_omnibus);
                        $this->product['wariant_cenowy'][$w_key]['omnibus_price'] = $omnibus_price;
                        

                        if ( $price_data[ $wariant_cenowy['bsid'] ] )
                        {
                            $wc = array();
                            $wc['cena']      = $price_data[ $wariant_cenowy['bsid'] ];
                            $wc['cena_base'] = $wariant_cenowy['cena']['brutto'];

                            if ( $this->format_settings['add_min_price'] )
                            {
                                if ( $promo )
                                {
                                    if ( $promo['pack_counter'] )
                                    {
                                        $this->product['pack_counter'] = $promo['pack_counter'];
                                    }
                                    
                                    $this->product['promo_ends'] = $promo['promo_ends'];

                                    if ( $wc['cena'] - $promo['wartosc_jedn_kuponu'] < 1 )
                                    {
                                        $wc['cena_min'] = 1;
                                    }
                                    else
                                    {
                                        $wc['cena_min'] = Tools::CalcNf( $wc['cena'], $promo['wartosc_jedn_kuponu'], 'sub' );
                                    }
                                    $wc['saving'] = Tools::CalcNf( $wc['cena_base'], $wc['cena_min'], 'sub' );
                                }
                                else
                                {
                                    $wc['cena_min'] = $wc['cena'];
                                    $wc['saving']  = Tools::CalcNf( $wc['cena_base'], $wc['cena'], 'sub' ); 
                                }
                            }
                            $wariant_cenowy = array_merge( $wariant_cenowy, $wc );
                        }
                        else
                        {
                            unset( $this->product['wariant_cenowy'][ $w_key ] );
                            continue;
                        }
                    }
                }
                else
                {
                    return false;
                }
//                dd($this->product['wariant_cenowy']);
                foreach ( $this->product['wariant_cenowy'] as $wariant )
                {
                    if ( $wariant['cena_min'] < $wariant['cena'] )
                    {
                        $this->product['offer_ends'] = $this->product['promo_ends'];
                        
                        if ( $this->product['pack_counter'] )
                        {
                            $this->product['item_counter'] = $this->product['pack_counter'];
                        }
                    }
                    else
                    {
                        $this->product['offer_ends'] = $this->product['rabat_ends'];
                    }
                    break;
                }
            break;
            
            case 'ubezpieczenia':
            case 'karty':
            case 'produkty_wirtualne':
                $sql = 'SELECT cena FROM ' . TABLE_PREFIX . 'cennik ';
                $sql.= 'WHERE id_produktu = "' . $this->product['identyfikator'] . '"';
                $price_data = $this->sdb->select_r( $sql );
                
                if ( $price_data )
                {
                    $this->product['cena'] = $price_data['cena'];
                    
                    if ( $this->format_settings['add_current_price_disp'] )
                    {
                        $this->product['cena_disp'] = Tools::CC( $price_data['cena'] , true );
                    }
                }
                break;
        }
        
        return true;
    }
    
    private function formatSetSingleCardsOnly()
    {
        if ( $this->product_type === 'karty' )
        {
            if ( ! $this->product['tylko_karta'] )
            {
                $this->product = array();
                return false;
            }
        }
        
        return true;
    }
    
    private function formatSetInsuranceStandAloneOnly()
    {
        if( $this->product_type === 'ubezpieczenia' )
        {
            if ( !$this->product['standalone'] )
            {
                $this->product = array();
                return false;
            }
        }
        
        return true;
    }
    
    private function formatResortString()
    {
        if ( $this->product['osrodki'] )
        {
            $resorts = '';

            foreach ( $this->product['osrodki'] as $resort )
            {
                $resorts.= $resort . ', ';
            }

            $resorts = rtrim( $resorts, ', ' );

            $this->product['osrodki'] = $resorts;
        }
        
        return true;
    }
    
    private function formatAvailable()
    {
        //  Sprawdzanie czy karnet jest wyłączony
        if ( $this->format_settings['set_available']['offline'] )
        {
            if ( !empty(Tools::GetOfferSuspendedProduct ( $this->product['identyfikator'], 'prid', $this->offer['parentid'] )) )
            {
                $this->product = array();
                return false;
            }
        }

        //  Sprawdzanie licznika karnetu
        if ( $this->format_settings['set_available']['counter'] )
        {
            if ( $this->product['ograniczone'] ) 
            {
                $this->product['item_counter'] = Userproducts::GetProductCounter( $this->product['identyfikator'] );
                /* if($this->product['identyfikator'] == '5f9bc4b8afaf9f36171c4934') { */
                /* dd($this->product); */
                /* } */

                
                if ( $this->product['item_counter'] < 1 )
                {
                    $this->product = array();
                    return false;
                }
            }
        }

        //  Sprawdzanie czy karnet jest dostępny do sprzedaży
        if ( $this->format_settings['set_available']['to_sell'] )
        {
            if ( $this->product['sprzedaz_od']['sec'] > $this->time->getTimestamp() || $this->product['sprzedaz_do']['sec'] < $this->time->getTimestamp() )
            {
                $this->product = array();
                return false;
            }
        }
        
        //  Sprawdzanie dostępności na stronie
        if ( $this->format_settings['set_available']['on_page'] )
        {
            if ( ! $this->product['dostepna_na_stronie'] )
            {
                $this->product = array();
                return false;
            }
        }
        
        return true;
    }
    
    private function formatDates()
    {
        if ( $this->product['obowiazuje'] )
        {
            
            if ( $this->product['obowiazuje']['data_od']['sec']  )
            {
                $data_od = date( $this->format_settings['add_dates'], $this->product['obowiazuje']['data_od']['sec'] );
                $this->product['obowiazuje']['data_od'] = $data_od;
            }

            if ( $this->product['obowiazuje']['data_do']['sec'] )
            {
                $data_do = date( $this->format_settings['add_dates'], $this->product['obowiazuje']['data_do']['sec'] );
                $this->product['obowiazuje']['data_do'] = $data_do;
            }

            if ( $this->product['obowiazuje']['time_start'] )
            {
                $czas_od = date( 'H:i', strtotime( $this->product['obowiazuje']['time_start'] ) );
                $this->product['obowiazuje']['time_start'] = $czas_od;
            }

            if ( $this->product['obowiazuje']['time_stop'] )
            {
                $czas_do = date( 'H:i', strtotime( $this->product['obowiazuje']['time_stop'] ) );
                $this->product['obowiazuje']['time_stop'] = $czas_do;
            }
        }

        if ( $this->product['sprzedaz_od']['sec'] )
        {
            $sprzedaz_od = date( $this->format_settings['add_dates'], $this->product['sprzedaz_od']['sec'] );
            $this->product['sprzedaz_od'] = $sprzedaz_od;
        }

        if ( $this->product['sprzedaz_do']['sec'] )
        {
            $sprzedaz_do = date( $this->format_settings['add_dates'], $this->product['sprzedaz_do']['sec'] );
            $this->product['sprzedaz_do'] = $sprzedaz_do;
        }
        
        return true;
    }
    
    
    /**
     * Formatowanie danych oferty
     * @return boolean
     */
    public function formatOffer()
    {
        if ( $this->offer )
        {
            if ( $this->format_settings['add_offer_id'] && $this->offer['_id'] )
            {
                $this->offer['id'] = $this->offer['_id']['$oid'];
                $this->offer_id = $this->offer['id'];
                unset( $this->offer['_id'] );
            }
            return true;
        }
        else
        {
            return false;
        }
    }
    
    public function formatOutputGTV( $product )
    {
        $result['identyfikator'] = $product['identyfikator'];
        $result['nazwa'] = $product['nazwa'];
        $result['podtytul'] = $product['podtytul'];
        $result['promo_end_date'] = $product['promo_end_data'];
        $result['promo_proc'] = $product['rabat'];
        
        $lowest_variant = array();
        foreach ( $product['wariant_cenowy'] as $variant )
        {
            if ( ! $lowest_variant )
            {
                $lowest_variant = $variant;
            }
            else
            {
                if ( $lowest_variant['cena'] > $variant['cena'] )
                {
                    $lowest_variant = $variant;
                }
            }
        }
        
        $result['cena'] = $lowest_variant['cena_base'];
        $result['cena_promo'] = $lowest_variant['cena_min'];
        if ( $product['obowiazuje'] )
        {
            $result['wazny_od'] = $product['obowiazuje']['data_od']['sec'];
            $result['wazny_do'] = $product['obowiazuje']['data_do']['sec'];
        }
        else
        {
            $result['wazny_od'] = '';
            $result['wazny_do'] = '';
        }
        
        if ( $product['ograniczone'] )
        {
            $result['ilosc'] = Userproducts::GetProductCounter( $product['identyfikator'] );
        }
        else
        {
            $result['ilosc'] = -1;
        }
        
        $result['dni'] = $product['liczba_dni'];
        return $result;
        
    }
    
    public function formatOutput( $type )
    {
        switch ( $type )
        {
            default:
                return false;
                
            case 'group_ticket_vouchers':
                if ( $this->output )
                {
                    $new_output= array();
                    
                    foreach ( $this->output as $offer )
                    {
                        $karnety = array();
                        
                        if ( $offer['karnety'] )
                        {
                            foreach ( $offer['karnety'] as &$ticket )
                            {
                                $karnety[] = $this->formatOutputGTV( $ticket );
                            }
                        }
                        
                        if ( $offer['vouchery'] )
                        {
                            foreach ( $offer['vouchery'] as &$ticket )
                            {
                                $karnety[] = $this->formatOutputGTV( $ticket );
                            }
                        }
                        
                        foreach ( $offer['partnerzy_sp'] as $partner )
                        {
                            $sklep = array();
                            $get_data = true;
                            
                            if ( $new_output )
                            {
                                foreach ( $new_output as &$o )
                                {
                                    if ( $o['id'] == $partner['id'] )
                                    {
                                        $get_data = false;
                                        
                                        if ( $o['karnety'] )
                                        {
                                            $o['karnety'] = array_merge( $o['karnety'], $karnety );
                                        }
                                        else
                                        {
                                            $o['karnety'] = $karnety;
                                        }
                                        
                                        break;
                                    }
                                }
                            }
                            
                            if ( $get_data )
                            {
                                $sql = 'SELECT name, description, domain, subdomain, city ';
                                $sql.= 'FROM ' . TABLE_PREFIX . 'partners WHERE id = ' . $partner['id'];
                                $sklep_data = $this->sdb->select_r( $sql );
                                if ( $sklep_data )
                                {
                                    $sklep['id'] = $partner['id'];
                                    $sklep['nazwa'] = $sklep_data['name'];
                                    $sklep['opis'] = $sklep_data['description'];
                                    $sklep['url'] = 'http://' . $sklep_data['subdomain'] . $sklep_data['domain'];
                                    $sklep['miasto'] = $sklep_data['city'];
                                    $sklep['img'] = '';
                                }

                                $sklep['karnety'] = $karnety;
                                $new_output[] = $sklep;
                            }
                        }
                    }
                    
                    $this->output = $new_output;
                }
                break;
                
            case 'ticket_packs':
                if ( $this->output )
                {
                    $produkt_typy['karty']              = App::_Lang('Karta','Oferty');
                    $produkt_typy['karnety']            = App::_Lang('Karnet','Oferty');
                    $produkt_typy['ubezpieczenia']      = App::_Lang('Ubezpieczenie','Oferty');
                    $produkt_typy['produkty_wirtualne'] = App::_Lang('Dodatek','Oferty');
                    $produkt_typy['vouchery']           = App::_Lang('Voucher','Oferty');
                    $pack_data = $this->output['pack_data'];
                    $tick_data = reset( $this->output['wariant_cenowy'] );
                    
                    if ( $pack_data )
                    {
                        foreach ( $pack_data as &$pack )
                        {
                            if ( $pack['produkty'] )
                            {
                                $pack['amount'] = 0;
                                foreach ( $pack['produkty'] as &$produkt )
                                {
                                    $this->setParentId( $pack['id_oferty'] );
                                    $this->setProductMgoId( $produkt['identyfikator'] );
                                    $this->setPOfferType( $produkt['p_type'] );
                                    $this->setOperators( 'product_name' );
                                    $this->getOffer();
                                    $this->setSingle();

                                    if ( $this->output[ $produkt['p_type'] ] )
                                    {
                                        $produkt = array_merge( $produkt, $this->output[ $produkt['p_type'] ] );
                                        $produkt['typ'] = $produkt_typy[ $produkt['p_type'] ];

                                        if ( $produkt['cena_min'] )
                                        {
                                            $pack['amount'] = Tools::CalcNF( $pack['amount'], $produkt['cena_min'] );
                                        }
                                    }
                                }
                            }
                        }
                    }
                    
                    
                    $this->output = array(
                        'ticket' => $tick_data,
                        'pack'   => $pack_data,   
                    );
                    
                    return true;
                }
                else
                {
                    return false;
                }
                
            case 'group_insurances':
                if( $this->output['ubezpieczenia'] )
                {
                    $ubezpieczenia = array();
                    foreach( $this->output['ubezpieczenia'] as $i_key => $insurance )
                    {
                        $pid = $insurance['partner_id'];
                        if( !$ubezpieczenia[$pid] )
                        {
                            $ubezpieczenia[$pid] = array(
                                'ubezpieczyciel' => $insurance['ubezpieczyciel'],
                                'partner_id'     => $pid,
                                'cena_min'       => $insurance['cena'],
                                'note1'       => $insurance['note1'],
                            );
                        }
                        if( $insurance['cena'] < $ubezpieczenia[$pid]['cena_min'] )
                        {
                            $ubezpieczenia[$pid]['cena_min'] = $insurance['cena'];
                        }
                    }
                    if( $ubezpieczenia )
                    {
                        $this->output['ubezpieczenia'] = $ubezpieczenia;
                    }
                    else
                    {
                        unset( $this->output['ubezpieczenia'] );
                    }
                }
                return true;
                
            case 'group_coupons':
                if ( $this->output )
                {
                    $coupons = array();
                    foreach ( $this->output as &$array )
                    {
                        if ( $array['parentid'] && $array['nazwa'] && $array['kupon_rabatowy'] )
                        {
                            $sql = 'SELECT e.id, el.value as note1 FROM ' . TABLE_PREFIX . 'kupony_emisje AS e ';
                            $sql.= 'JOIN ' . TABLE_PREFIX . 'kupony_emisje_oferty AS o ';
                            $sql.= 'ON o.oferta = "' . $array['parentid'] . '" ';
                            $sql.= 'AND e.id = o.emisja ';
                            $sql.= 'AND e.rodzaj_kuponu = "USER-PREPAID" ';
                            $sql.= 'AND e.status = 1 ';
                            $sql.= 'LEFT JOIN '.TABLE_PREFIX.'kupony_emisje_langs el ON el.parentid = e.id AND el.langid = '.LANG.' AND el.property = "opis"';
                            $cpn = $this->sdb->select_r($sql);
//                            $sql = 'SELECT e.id FROM ' . TABLE_PREFIX . 'kupony_emisje AS e ';
//                            $sql.= 'JOIN ' . TABLE_PREFIX . 'kupony_emisje_oferty AS o ';
//                            $sql.= 'ON o.oferta = "' . $array['parentid'] . '" ';
//                            $sql.= 'AND e.id = o.emisja ';
//                            $sql.= 'AND e.rodzaj_kuponu = "USER-PREPAID" ';
//                            $sql.= 'AND e.status = 1';
//                            $cpn = $this->sdb->select_r($sql);

                            if ( $cpn )
                            {
                                $array['id_emisji'] = $cpn['id'];
                                $coupons[] = $array;
                            }
                        }
                    }
                    if ( $coupons )
                    {
                        $this->output['kupony'] = $coupons;
                    }
                    return true;
                }
                break;
                
            case 'insurance_type':
                #sprawdzać czy karnet jest już w koszyku

                $insurances = array(
                    'normal' => array(),
                    'standalone' => array(),
                );
                
                foreach ( $this->output as $offer_insurances )
                {
                    if(!empty($offer_insurances['ubezpieczenia'])){
                        foreach ( $offer_insurances['ubezpieczenia'] as $i )
                        {
                            $pid = $i['parentid'];
                            $insurances['normal'][ $pid ][] = $i;

                            if ( $i['standalone'] )
                            {
                                foreach ( $insurances['standalone'] as $standalone )
                                {
                                    $diff = array_diff_assoc( $standalone, $i );

                                    if ( count( $diff ) == 2 && $diff['identyfikator'] && $diff['parentid'] )
                                    {
                                        continue 2;
                                    }

                                }

                                $insurances['standalone'][] = $i;
                            }
                        }
                    }
                }
                
                if ( $this->input )
                {
                    foreach ( $this->input as $c_key => $card )
                    {
                        foreach ( $card['karnet'] as $t_key => $ticket )
                        {
                            $p_dni = $ticket['pozostalo_dni'];
                            $p_id  = $ticket['parentid'];
                            
                            if ( $insurances['normal'][ $p_id ] )
                            {
                                foreach ( $insurances['normal'][ $p_id ] as $offer_insurance )
                                {
                                    if ( $offer_insurance['czas_trwania'] == $p_dni )
                                    {
                                        continue 2;
                                    }
                                }
                                
                                unset( $this->input[ $c_key ]['karnet'][ $t_key ] );
                            }
                            else
                            {
                                unset( $this->input[ $c_key ]['karnet'][ $t_key ] );
                            }
                            
                            if ( empty( $card['karnet'] ) )
                            {
                                unset( $this->input[ $c_key ] );
                            }
                        }
                    }
                }
                else
                {
                    unset( $insurances['normal'] );
                }

                $this->output = array(
                    'insurances' => $insurances,
                    'cards' => $this->input,
                );
                
                break;

            case 'sort_products':
            $product_types = array( 'karty', 'karnety', 'vouchery' );

            foreach ( $product_types as $product )
            {
                if ( $this->output[ $product ] )
                {
                    usort($this->output[ $product ], function($a, $b) {
                        return $a['sortowanie'] - $b['sortowanie'];
                    });
                }
            }


            break;
        }
    }
    
    public function cleanOutput()
    {
        $this->output = array();
        $this->errors = array();
        return true;
    }
    
    public function cleanFormatSettings()
    {
        $this->format_settings = array();
        return true;
    }
    
    public function formatInsurance( $set_output = false )
    {
        if ( $this->insurance )
        {
            //  Sprawdzanie czy karnet jest wyłączony
            if ( $this->insurance_settings['set_available']['offline'] )
            {
                if ( !empty(Tools::GetOfferSuspendedProduct ( $this->insurance['identyfikator'], 'prid', $this->offer['parentid'] )) )
                {
                    $this->insurance = array();
                    return false;
                }
            }
            
            //  Sprawdzajnie licznika karnetu
            if ( $this->insurance_settings['set_available']['counter'] )
            {
                if ( isset( $this->insurance['ograniczone'] ) === true )
                {
                    if ( $this->insurance['ograniczone'] && ( Userproducts::GetProductCounter( $this->insurance['identyfikator'] ) < 1 ) ) 
                    {
                        $this->insurance = array();
                        return false;
                    }
                }
                else
                {
                    $this->errors['formatInsurance'][] = App::_Lang('Brak pola "ograniczone" do sprawdzenia stanu licznika','Oferty');
                }
            }
            
            //  Sprawdzanie czy karnet jest dostępny do sprzedaży
            if ( $this->insurance_settings['set_available']['to_sell'] )
            {
                if ( $this->insurance['sprzedaz_od']['sec'] && $this->insurance['sprzedaz_do']['sec'] )
                {
                    if ( $this->insurance['sprzedaz_od']['sec'] > $this->time->getTimestamp() )
                    {
                        $this->insurance = array();
                        return false;
                    }

                    if ( $this->insurance['sprzedaz_do']['sec'] < $this->time->getTimestamp() )
                    {
                        $this->insurance = array();
                        return false;
                    }
                }
                else
                {
                    $this->errors['formatInsurance'][] = App::_Lang('Brak pól "sprzedaz_od" / "sprzedaz_do" do sprawdzenia daty','Oferty');
                }
            }
            
            //  Sprawdzanie dostępności na stronie
            if ( $this->insurance_settings['set_available']['on_page'] )
            {
                if ( isset( $this->insurance['dostepna_na_stronie'] ) )
                {
                    if ( $this->insurance['dostepna_na_stronie'] === false )
                    {
                        $this->insurance = array();
                        return false;
                    }
                }
                else
                {
                    $this->errors['formatInsurance'][] = App::_Lang('Brak pola "dostepna_na_stronie" do sprawdzenia dostępności','Oferty');
                }
            }
            
            if ( $this->insurance_settings['add_current_price'] )
            {
                if ( $this->insurance['identyfikator'] )
                {
                    $sql = 'SELECT cena FROM ' . TABLE_PREFIX . 'cennik ';
                    $sql.= 'WHERE id_produktu = "' . $this->insurance['identyfikator'] . '"';
                    $price_data = $this->sdb->select_r( $sql );
                    
                    if ( $price_data )
                    {
                        $this->insurance['cena'] = $price_data['cena'];
                        $this->insurance['cena_disp'] = Tools::CC( $this->insurance['cena'], true );
                    }
                    else
                    {
                        $this->errors['formatInsurance'][] = App::_Lang('Nie pobrano ceny z cennika','Oferty');
                    }
                }
                else
                {
                    $this->errors['formatInsurance'][] = App::_Lang('Brak identyfikatora ubezpieczenia','Oferty');
                }
            }
            
            //  Formatowanie ośrodków w string
            if ( $this->insurance_settings['set_resorts_string'] )
            {
                if ( $this->insurance['osrodki'] )
                {
                    $resorts = '';

                    foreach ( $this->insurance['osrodki'] as $resort )
                    {
                        $resorts.= $resort . ', ';
                    }

                    $resorts = rtrim( $resorts, ', ' );

                    $this->insurance['osrodki'] = $resorts;
                }
                else
                {
                    $this->insurance['formatInsurance'][] = App::_Lang('Brak ośrodków do stworzenia ciągu','Oferty');
                }
            }
            
            if ( $set_output )
            {
                $this->result = true;
                $this->output = $this->insurance;
            }
            
            return true;
        }
        else
        {
            $this->errors['formatInsurance'][] = App::_Lang('Brak danych ubezpieczenia do sformatowania','Oferty');
            return false;
        }
    }
    
    
    
    public function formatInsurances( $set_output = false )
    {
        $this->result = false;
        
        if ( $this->output['ubezpieczenia'] )
        {
            foreach ( $this->output['ubezpieczenia'] as $insurance_key => &$insurance )
            {
                $this->insurance = $insurance;
                
                if ( ! $this->formatInsurance() )
                {
                   unset( $this->output['ubezpieczenia'][ $insurance_key ] );
                   continue;
                }
                
                $insurance = $this->insurance;
            }
           
            if ( $this->output['ubezpieczenia'] )
            {
               if ( $set_output )
               {
                   $this->output = $this->output['ubezpieczenia'];
               }
            }
            else
            {
                $this->errors['formatInsurances'][] = App::_Lang('Formatowanie usunęło wszystkie ubezpieczenia','Oferty');
            }
            $this->result = true;
            return true;
        }

        $this->errors['formatInsurances'][] = App::_Lang('Brak ubezpieczeń do sformatowania','Oferty');
        return false;
    }
    
    public function formatCards( $set_output = false )
    {
        $this->result = false;
        
        if ( $this->output['karty'] )
        {
            foreach ( $this->output['karty'] as $card_key => &$card )
            {
                $this->card = $card;
                
                if ( ! $this->formatCard() )
                {
                   unset( $this->output['karty'][ $card_key ] );
                   continue;
                }
                
                $card = $this->card;
            }
           
            if ( $this->output['karty'] )
            {
               if ( $set_output )
               {
                   $this->output = $this->output['karty'];
               }
            }
            else
            {
                $this->errors['formatCards'][] = App::_Lang('Formatowanie usunęło wszystkie karty','Oferty');
            }
            $this->result = true;
            return true;
        }

        $this->errors['formatCards'][] = App::_Lang('Brak kart do sformatowania','Oferty');
        return false;
    }

    public function addCalendar()
    {
        foreach(PriceCalendarModel::getPriceCalendarForOffer($this->output[0]['parentid']) as $offerrId => $ptypes) {
            foreach($ptypes as $ptype => $products) {
                foreach($products as $product => $variants) {
                    foreach($variants as $variant) {
                        foreach($this->output[$ptype] as $k => $productData) {
                            if($productData['identyfikator'] == $product) {
                                $this->output[$ptype][$k]['calendarData'] = $variants;
                            }
                        }
                    }
                }
            }

        }
    }
}

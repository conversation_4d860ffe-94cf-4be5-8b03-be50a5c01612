<?php

use MongoDB\BSON\ObjectId as MongoId;
use MongoDB\BSON\Regex as MongoRegex;
use MongoDB\BSON\UTCDateTime as MongoDate;

/**
 * Sterownik i kontroler do bazy danych MONGO
 * @package Application
 * @subpackage Database
 * <AUTHOR>
 * @usage Aktywacja klasy przez activate();
 */
class Mymongo
{
    //put your code here
    public $handler = null;
    public $db = null;
    protected static $mgouser = MONGOUSER;
    protected static $mgopass = MONGOPASS;
    protected static $mgoname = MONGONAME;
    private static $active = null;
    protected $errormsg = '';
    protected $errorno = '';
    protected $errorfile = '';
    protected $errorline = '';
    public $mongoVersion = [
        'major' => 3,
        'minor' => 0,
        'mini' => 15
    ];
    public $errortrace = '';
    public $error = false;
    private $debug = true;
    public $currentoffer = array(); // aktualna oferta

    protected function __construct($mgouser, $mgopass, $mgoname, $mgohost = MONGOHOST, $mgoport = MONGOPORT)
    {
        $cstring = 'mongodb://' . $mgouser . ':' . $mgopass . '@' . $mgohost . ':' . $mgoport . '/' . $mgoname;
        try {
            $this->handler = new MongoDB\Client($cstring);

        } catch (MongoConnectionException $exc) {
            $this->errormsg = $exc->getMessage();
            $this->errorfile = $exc->getFile();
            $this->errorline = $exc->getLine();
            $this->errorno = $exc->getCode();
            $this->error = "Warehouse said there is connection error nr " . $this->errorno;
            if ($this->debug) {
                $this->errortrace = $exc->getTrace();
            }
            file_put_contents('mongoerr.txt', var_export($this->errormsg, true));
            self::$active = false;
        }
        if (self::$active !== false) {
            $this->db = $this->handler->selectDatabase($mgoname);
        } else {
            die('Mongo gone....');
        }
    }

    protected function getVersion()
    {
        return $this->mongoVersion['major'] . '.' . $this->mongoVersion['minor'] . '.' . $this->mongoVersion['mini'];
    }

    /**
     * Aktywacja klasy (statyczna)
     * @param string $mgouser Nazwa usera
     * @param string $mgopass Hasło
     * @param string $mgoname Nazwa kolekcji
     * @return Object
     */
    public static function activate($mgouser = null, $mgopass = null, $mgoname = null): self
    {
        if (is_null(self::$active)) {
            is_null($mgouser) ? $mgouser = self::$mgouser : '';
            is_null($mgopass) ? $mgopass = self::$mgopass : '';
            is_null($mgoname) ? $mgoname = self::$mgoname : '';
            self::$active = new Mymongo($mgouser, $mgopass, $mgoname);
        }

        return self::$active;
    }

    /**
     * Pobiera listę ofert
     * @param string/array $fields pola do pobrania
     * @param array $where kwerenda
     * @param string $orderby
     * @param int $limit
     * @param bool $ret
     * @return array
     */
    public function mget_offers_list($fields, $where, $orderby = null, $limit = null, $ret = false)
    {
        $projection = $this->makeProjection($fields);
        $aWhere = array();
        $aWhere['status'] = ['$exists' => true];
        foreach ($where as $k => $v) {
            switch ($k) {
                case 'offername':
                    $aWhere['nazwa.' . LANG] = array('$regex' => new MongoRegex('.*' . $v . '.*', 'i'));
                    break;
                case 'offerids':
                    $aWhere['identyfikator'] = array('$regex' => new MongoRegex('.*' . $v . '.*', 'i'));
                    break;
                case 'dataod':
                    $aWhere['data_od'] = array('$gt' => new MongoDate(strtotime($v) * 1000));
                    break;
                case 'datado':
                    $aWhere['data_do'] = array('$lt' => new MongoDate(strtotime($v) * 1000));
                    break;
                case 'osrodek':
                    $aWhere['osrodki.' . $v] = array('$exists' => true);
                    break;
                case 'sprzedawca':
                    $aWhere['partnerzy_sp.id'] = $v;
                    break;
                case 'ubezpieczyciel':
                    $aWhere['ubezpieczenia.partner_id'] = $v;
                    break;
                case 'status':
                    $aWhere['status.id'] = $v;
                    break;
                default:
                    $aWhere[$k] = $v;
                    break;
            }

        }
        $aWhere['current'] = 1;
        $sort = array('nazwa' => 1);

        $options = [
            'sort' => ['nazwa.'.LANG => 1],
            'limit' => $limit
        ];

        if (count($projection) == 0) {
            $cursor = $this->handler->{self::$mgoname}->offers->find($aWhere, $options);
        } else {
            $options['projection'] = $projection;
            $cursor = $this->handler->{self::$mgoname}->offers->find($aWhere, $options);
        }

        foreach ($cursor->toArray() as $v) {
            $set = $this->Mgo2array($v);
            $row['id'] = $set['_id']['$oid'];
            $row['parentid'] = $set['parentid'];
            $row['nazwa'] = $set['nazwa'][LANG];
            $row['identyfikator'] = $set['identyfikator'];
            if(!is_array($set['osrodki'])) {
                file_put_contents('offererror.txt', date('YmdHis') . $set['_id']['$oid'] . PHP_EOL, FILE_APPEND);
                continue;
            }
            foreach ($set['osrodki'] ?? [] as $vv) {
                $aNazwy[] = $vv;
            }
            $row['osrodki'] = implode(', ', $aNazwy);
            unset($aNazwy);
            foreach ($set['partnerzy_sp'] ?? [] as $vv) {
                $aSprzedawcy[] = $vv['nazwa'];
            }
            $row['sprzedawcy'] = join(', ', $aSprzedawcy);
            unset($aSprzedawcy);
            $row['data_od'] = date('Y-m-d', $set['data_od']['$date']['$numberLong'] / 1000);
            $row['data_do'] = date('Y-m-d', $set['data_do']['$date']['$numberLong'] / 1000);
            $row['status'] = $set['status']['nazwa'];
            $row['status.id'] = $set['status']['id'];
            $row['offer'] = $set;
            $mgobj[] = $row;
        }
        $out = &$mgobj;
        if ($ret) {
            die(var_dump($out));
        }
        return $out;
    }

    /**
     * Pobiera listę ofert
     * @param string/array $fields pola do pobrania
     * @param array $where kwerenda
     * @param string $orderby
     * @param int $limit
     * @param bool $ret
     * @return array
     */
    public function mget_user_offers_list($fields, $where, $orderby = null, $limit = null, $ret = false, $partner_id = 22)
    {
        $projection = $this->makeProjection($fields);
        $aWhere = array();
        $aWhere['current'] = 1;
        $aWhere['partnerzy_sp.' . $partner_id] = array('$exists' => true);
        $aWhere['status.id'] = 5;

        $options = [
            'sort' => ['nazwa' => 1],
            'limit' => $limit
        ];

        if (count($projection) == 0) {
            $cursor = $this->handler->{self::$mgoname}->offers->find($aWhere, $options);
        } else {
            $options['projection'] = $projection;
            $cursor = $this->handler->{self::$mgoname}->offers->find($aWhere, $options);
        }
        foreach ($cursor as $v) {
            $set = $this->Mgo2array($v);
            $row['id'] = $set['_id']['$oid'];
            $row['parentid'] = $set['parentid'];
            $row['nazwa'] = $set['nazwa'][LANG];
            $row['identyfikator'] = $set['identyfikator'];
            foreach ($set['osrodki'] as $vv) {
                $aNazwy[] = $vv;
            }
            $row['osrodki'] = join(', ', $aNazwy);
            unset($aNazwy);
            $row['data_od'] = date('Y-m-d', $set['data_od']['$date']['$numberLong'] / 1000);
            $row['data_do'] = date('Y-m-d', $set['data_do']['$date']['$numberLong'] / 1000);
            $row['status'] = $set['status']['nazwa'];
            $row['status.id'] = $set['status']['id'];
            $row['offer'] = $set;
            $mgobj[] = $row;
        }
        $out = &$mgobj;

        if ($ret) {
            die(var_dump($out));
        }
        return $out;
    }

    /**
     * Pobiera listę wszystkich aktualnych ofert (bez sprawdzania daty)
     *
     *      * @return array
     */
    public function mget_active_offers_list($statusid = 5)
    {
        $aWhere = array();
        $aWhere['current'] = 1;
        $aWhere['status.id'] = $statusid;
        $sort = array('nazwa' => 1);
        $cursor = $this->db->offers->find($aWhere, ['sort' => $sort]);
        foreach ($cursor as $v) {
            $set = $this->Mgo2array($v);
            $row['offer'] = $set;
            $mgobj[] = $row;
        }
        $out = &$mgobj;
        return $out;
    }

    /**
     * Pobiera listę wszystkich zaakceptowanych ofert (status 4,5[,6,7])
     *
     * @return array
     */
    public function mget_accepted_offers_list($anulowana = false, $nieaktywna = false)
    {
        $aWhere = array();
        $aWhere['current'] = 1;
        $statusy[] = array('status.id' => 4);
        $statusy[] = array('status.id' => 5);
        if ($anulowana) {
            $statusy[] = array('status.id' => 6);
        }
        if ($nieaktywna) {
            $statusy[] = array('status.id' => 7);
        }
        $aWhere['$or'] = $statusy;
        $sort = array('nazwa' => 1);
        $options = [];

        $proj['nazwa'] = '1';
        $proj['parentid'] = '1';
        $proj['status'] = '1';

        $options['projection'] = $proj;
        $options['sort'] = $sort;
        $cursor = $this->db->offers->find($aWhere, $options);
        foreach ($cursor as $v) {
            $set = $this->Mgo2array($v);
            $mgobj[$set['parentid']] = $set;
        }
        return $mgobj;
    }

    /**
     * Pobiera listę produktów danego typu
     * @param string $offerid Id oferty (parentid)
     * @param string $ptype Typ produktu, który chcemy wyciąfnąć (Wg ofert z Mongo)
     * @param bool $full Jeżelik true zwraca tabelę z wszytkimi informacjami o produkcie z Mongo
     * @return type
     */
    public function mget_product_list($offerid, $ptype, $full = false)
    {
        $aWhere = array();
        $aWhere['current'] = 1;
        $aWhere['parentid'] = $offerid;
        $ptype == 'karty' ? $suffix = 'podtytul' : $suffix = 'nazwa';
        $proj['_id'] = 0;
        $proj[$ptype . '.identyfikator'] = 1;
        if ($full) {
            $proj[$ptype] = 1;
        } else {
            $proj[$ptype . '.' . $suffix] = '1';
        }
        $cursor = $this->db->offers->findOne($aWhere, ['projection' => $proj]);
        return $this->Mgo2array($cursor);
    }

    /**
     * Pobiera obiekt danej oferty
     * @param string $offerid
     * @param bool $ret
     * @return array Tablica asocjacyjna
     */
    public function mget_offer_details($offerid, $ret = false)
    {
        $aWhere = array('parentid' => $offerid, 'current' => 1);
        $cursor = $this->db->offers->findOne($aWhere);
        $cursor = $this->Mgo2array($cursor);
        $this->normalizeReadDates($cursor);
//        die(var_dump($cursor));
        if ($ret) {
            die(var_dump($cursor));
        }
        return $cursor;
    }

    public function normalizeReadDates(&$offer)
    {
        if(empty($offer)) {
            return;
        }
        foreach($offer as $k=>&$v) {
            if(is_array($v) && !empty($v['$date'])) {
                $v['sec'] = substr($v['$date']['$numberLong'], 0, -3);
            }
            if(is_array($v) && empty($v['$date'])) {
                $this->normalizeReadDates($v);
            }
        }
    }


    public function normalizeSaveDates(&$offer)
    {
        foreach($offer as $k=>&$v) {
            if(is_array($v) && !empty($v['$date'])) {
                $v = new MongoDate($v['$date']['$numberLong']);
            }
            if(is_array($v) && empty($v['$date'])) {
                $this->normalizeSaveDates($v);
            }
        }
    }

    /**
     * Pobiera dokument bez zamiany na tablicę (od razu do zapisania w bazie). oferty. Może od razu modyfikować pole current na 0
     * @param string $offerid ParentID oferty
     * @param bool $modify Jeżeli true - modyfikuje pole current na 0
     * @param bool $ret
     * @return object
     */

    public function mget_offer_source($offerid, $modify = true, $ret_type = 'object', $ret = false)
    {

        $aWhere = ['parentid' => $offerid, 'current' => 1];
        if ($modify) {
            $cursor = $this->db->offers->findOneAndUpdate(
                $aWhere,
                ['$set' => ['current' => 0]],
                ['returnDocument' => MongoDB\Operation\FindOneAndUpdate::RETURN_DOCUMENT_AFTER]
            );
        } else {
            $cursor = $this->db->offers->findOne($aWhere);
        }
        if ($ret) {
            die(var_dump($cursor));
        }

        if ($ret_type === 'array') {
            $cursor = $this->Mgo2array($cursor);
            $this->normalizeReadDates($cursor);
        }

        return $cursor;
    }

    /**
     * Zamienia obiekt MongoCursor na tablicę
     * @param object $mgobj
     * @return array
     */
    public function Mgo2array($mgobj)
    {
        return json_decode(json_encode($mgobj), true);
    }

    /**
     * Zapisuje dokument
     * @param array $doc
     * @param bool $new Jeżeli true - zapisuje nowy dokument ustawiając odpowiednio parentID
     * @param string $collection Nazwa kolekcji
     * @return type
     */
    public function saveOffer($doc, $new = false, $collection = 'offers')
    {
            $this->normalizeSaveDates($doc);
            $res = $this->db->$collection->insertOne($doc);
            if ($res->getInsertedCount() == 0) {
                return null;
            }
            if ($new) {
                $update_res = $this->db->$collection->updateOne(['_id' => $res->getInsertedId()], ['$set' => ['parentid' => $res->getInsertedId()->__toString()]]);
            }
            return $res->getInsertedId();
    }

    /**
     * Zapisuje dokument
     * @param Array $doc
     * @param bool $new Jeżeli true - zapisuje nowy dokument ustawiając odpowiednio parentID
     * @param string $collection Nazwa kolekcji
     * @return type
     */
    public function saveDocument($doc, $collection = 'offers')
    {
        $res = $this->db->$collection->insertOne($doc);
        return $res->getInsertedId()->__toString();
    }

    public function updateDocument($doc, $id_mongo, $collection = 'offers')
    {
//        dd( $id_mongo, $doc);
        $id_mongo = new MongoId($id_mongo);
        $res = $this->db->$collection->updateOne(['_id' => $id_mongo], $doc);
        return $res->getUpsertedId()->__toString();
    }

    public function replaceDocument($doc, $id_mongo, $collection = 'offers')
    {
        $id_mongo = new MongoId($id_mongo);
        $res = $this->db->$collection->replaceOne(['_id' => $id_mongo], $doc);
        return $res->getUpsertedId();
    }

    public function removeDocument($id_mongo, $collection = 'basket')
    {
        $id_mongo = new MongoId($id_mongo);
        $this->db->$collection->deleteOne(['_id' => $id_mongo]);
    }

    /**
     * @param $id
     * @param string $collection
     * @param string $type 'object' || 'array'
     * @return mixed
     */
    public function getDocument($id, $collection = 'offers', $type = 'object')
    {
        $res = $this->db->$collection->findOne(['_id' => $id]);
        if (empty($res)) {
           return null;
        }

        if($type === 'object') {
            return $res;
        }

        $array = $this->Mgo2array($res);
        $this->normalizeReadDates($array);

        return $array;
    }

    /**
     * Pobiera informacje o karcie
     * @param int $cardid
     * @param string $offerid ParentID
     * @param bool $ret
     * @return array
     */
    public function mget_card_details($cardid, $offerid, $ret = false)
    {
//        $offer_id = new MongoId($offerid);
        $where = [
            'parentid' => $offerid,
            'current' => 1,
            'karty.' . $cardid => [
                '$exists' => 1
            ]
        ];
        $proj = ['karty' => 1, "data_od" => 1, "data_do" => 1];
//        $proj = array('karty.$'=>1,"data_od"=>1,"data_do"=>1);        
        $cursor = $this->db->offers->findOne($where, ['projection' => $proj]);
        if ($ret) {
            die(var_dump($cursor));
        }
        $cursor['karty'][$cardid]['offerdata_od'] = $cursor['data_od'];
        $cursor['karty'][$cardid]['offerdata_do'] = $cursor['data_do'];
        $ret = $this->Mgo2array($cursor['karty'][$cardid]);
        $this->normalizeReadDates($ret);
        return $ret;
    }

    /**
     * Pobiera informacje o produkcie wirtualnym
     * @param int $cardid
     * @param string $offerid ParentID
     * @param bool $ret
     * @return array
     */
    public function mget_virtualp_details($cardid, $offerid, $ret = false)
    {
//        $offer_id = new MongoId($offerid);
        $cardid = intval($cardid);
        $where = array(
            'parentid' => $offerid,
//            '_id'=>$offer_id,
            'current' => 1,
            'produkty_wirtualne.' . $cardid => array(
                '$exists' => 1
            )
        );
        $proj = array();
//        $proj = array('produkty_wirtualne.$'=>1,"data_od"=>1,"data_do"=>1);        
        $cursor = $this->db->offers->findOne($where, $proj);
        if ($ret) {
            die(var_dump($cursor));
        }
        $this->currentoffer = $this->Mgo2array($cursor);
        $cursor['produkty_wirtualne'][$cardid]['offerdata_od'] = $cursor['data_od'];
        $cursor['produkty_wirtualne'][$cardid]['offerdata_do'] = $cursor['data_do'];
        $ret = $this->Mgo2array($cursor['produkty_wirtualne'][$cardid]);
        $this->normalizeReadDates($ret);
        return $ret;
    }

    /**
     * Pobiera informacje o bilecie (karnecie)
     * @param int $ticketid
     * @param string $offerid
     * @param bool $ret
     * @return array
     * @noinspection ForgottenDebugOutputInspection
     */
    public function mget_ticket_details($ticketid, $offerid, $ret = false)
    {

        $where = array(
            'parentid' => $offerid,
            'current' => 1,
            'karnety.' . intval($ticketid) => array(
                '$type' => 3
            )
        );

        $proj = array();
        $cursor = $this->db->offers->findOne($where, $proj);
        if ($ret) {
            die(var_dump($cursor));
        }
        $this->currentoffer = $this->Mgo2array($cursor);
        $cursor['karnety'][intval($ticketid)]['offerdata_od'] = $cursor['data_od'];
        $cursor['karnety'][intval($ticketid)]['offerdata_do'] = $cursor['data_do'];
        $ret = $this->Mgo2array($cursor['karnety'][intval($ticketid)]);
        $this->normalizeReadDates($ret);
        return $ret;
    }

    /**
     * Pobiera informacje na temat ubezpieczeniń w ofercie
     * @param int $insid ID ubezpieczenia
     * @param string $offerid ID oferty
     * @param bool $ret
     * @return array
     * @noinspection ForgottenDebugOutputInspection
     */
    public function mget_insurance_details($insid, $offerid, $ret = false)
    {
        $where = [
//            '_id'=>$offer_id,
            'parentid' => $offerid,
            'current' => 1,
            'ubezpieczenia.' . $insid => [
                '$type' => 3
            ]
        ];
        $proj = ['ubezpieczenia' => 1, "data_od" => 1, "data_do" => 1];
        $cursor = $this->db->offers->findOne($where, ['projection' => $proj]);
        if ($ret) {
            die(var_dump($cursor));
        }
        $cursor['ubezpieczenia'][$insid]['offerdata_od'] = $cursor['data_od'];
        $cursor['ubezpieczenia'][$insid]['offerdata_do'] = $cursor['data_do'];
        $ret = $this->Mgo2array($cursor['ubezpieczenia'][intval($insid)]);
        $this->normalizeReadDates($ret);
        return $ret;
    }


    /**
     * Pobiera informacje na partnerów 'osrodki' i 'partnerzy sprzedazowi' z oferty
     * @param string $offerid ID oferty
     * @param bool separate Jeżeli false, wszystkie id zwracane są w tabeli numerycznej, jeżeli TRUE - tabela dzielona jest asocjacyjnie na typy ośrodków
     * @param bool $ret
     * @return bool
     * @return array Tablica podzielona na typ ośrodka. Jako wartości ID partnerów
     * @noinspection ForgottenDebugOutputInspection
     */
    public function mget_offer_partners($offerid, $separate = false, $ret = false)
    {

        $where = [
            'parentid' => $offerid,
            'current' => 1
        ];
        $proj = ['partnerzy_sp' => 1, 'osrodki' => 1];
        $cursor = $this->db->offers->findOne($where, ['projection' => $proj]);
        if ($ret) {
            die(var_dump($cursor));
        }
        $partnerzy = array();
        $cursor = $this->Mgo2array($cursor);
        if ($separate) {
            foreach ($cursor['osrodki'] as $k => $v) {
                $partnerzy['osrodki'][] = $k;
            }
            foreach ($cursor['partnerzy_sp'] as $k => $v) {
                $partnerzy['partnerzy_sp'][] = $v['id'];
            }
        } else {
            foreach ($cursor['osrodki'] as $k => $v) {
                $partnerzy[] = $k;
            }
            foreach ($cursor['partnerzy_sp'] as $k => $v) {
                $partnerzy[] = $v['id'];
            }
        }
        $this->normalizeReadDates($partnerzy);
        return $partnerzy;
    }

    /**
     * Zwraca detale vouchera z oferty
     * @param type $vid
     * @param type $offerid
     * @param type $ret
     * @return type
     * @noinspection ForgottenDebugOutputInspection
     */
    public function mget_voucher_details($vid, $offerid, $ret = false)
    {
//        $offer_id = new MongoId($offerid);
        $vid = intval($vid);
        $where = array(
//            '_id'=>$offer_id,
            'parentid' => $offerid,
            'current' => 1,
            'vouchery.' . $vid => array(
                '$type' => 3
            )
        );
//        $proj = array('vouchery.$'=>1,"data_od"=>1,"data_do"=>1);        
        $proj = array();
        $cursor = $this->db->offers->findOne($where, $proj);
        if ($ret) {
            die(var_dump($cursor));
        }
        $this->currentoffer = $this->Mgo2array($cursor);
        $cursor['vouchery'][$vid]['offerdata_od'] = $cursor['data_od'];
        $cursor['vouchery'][$vid]['offerdata_do'] = $cursor['data_do'];
        return $this->Mgo2array($cursor['vouchery'][$vid]);
    }

    /**
     * Aktualizacjia statusu ofery
     * @param string $offerid ID oferty (Mongo)
     * @param int $statusid ID statusu (Mysql)
     * @param string $status Treść statusu
     * @return type
     */
    public function mset_status($offerid, $statusid, $status)
    {
//        $offer_id = new MongoId($offerid);
        $where = array(
            'parentid' => $offerid,
            'current' => 1
        );
        $change = array(
            '$set' => array(
                'status' => array(
                    'id' => $statusid,
                    'nazwa' => $status
                )
            )
        );
        $cursor = $this->db->offers->updateOne($where, $change);
        return $cursor;
    }

    public function getOldBaskets($date, $limit = 5000)
    {
        $mgobj = [];
        $where = [
            'data_koszyka' => [
                '$lt' => $date
            ]
        ];
        $projecttion = [
            '_id' => 1,
            'data_koszyka' => 1
        ];

        $options = [
            'limit' => $limit,
            'projection' => $projecttion
        ];
        $cursor = $this->db->basket->find($where, $options);
        foreach ($cursor as $v) {
            $set = $this->Mgo2array($v);
            $mgobj[] = $set;
        }
        return $mgobj;
    }

    public function removeOldBaskets($date)
    {
        $where = array(
            'data_koszyka' => array(
                '$lt' => $date
            )
        );
        $this->db->basket->deleteMany($where);
    }

    /**
     * Buduje projekcję do zapytania z pól $fields
     * @param array/string $fields lista pól przdzielona ',' lub w tablicy
     * @return array
     */
    public function makeProjection($fields)
    {
        if (!is_array($fields)) {
            if (is_string($fields)) {
                $aFields = explode(',', $fields);
            } else {
                $aFields = array();
            }
        } else {
            $aFields = &$fields;
        }
        $pr = array();
        foreach ($aFields as $field) {
            $pr[$field] = 1;
        }

        return $pr;
    }

    /**
     * Zwraca tablicę gdzie kluczami są dane z pola ID
     * @param array $array tablica z danymi
     * @return array
     */
    public function arrayById($array)
    {
        foreach ($array as $k => $v) {
            $el[$v['id']] = $v;
        }
        return $el;
    }

    /**
     * Pobranie informacji o produkcie z oferty
     * @param type $pid identyfikator
     * @param type $ptype string z nazwą produktu z mongodb
     * @param type $ret
     * @return type
     * @noinspection ForgottenDebugOutputInspection
     */
    public function mget_product_details($pid, $ptype, $ret = false)
    {
        $where = [
            'current' => 1,
            $ptype . '.identyfikator' => $pid,
        ];
        $proj = [$ptype . '.$' => 1, 'parentid' => 1];
        $cursor = $this->db->offers->findOne($where, ['projection' => $proj]);
        if ($ret) {
            die(var_dump($cursor));
        }
        $cursor[$ptype][0]['parentid'] = $cursor['parentid'];
        $cursor[$ptype][0]['qry'] = ['where' => $where, 'proj' => $proj];
        return $this->Mgo2array($cursor[$ptype][0]);
    }

    public function mget_order_details($id)
    {
        $where = [
            '_id' => new MongoId($id),
        ];

        $proj = array();
        $cursor = $this->db->orders->findOne($where, $proj);
        return $this->Mgo2array($cursor);
    }

    /**
     * Zwraca zamowienia użytkownika
     * @param type $userid int id usera
     * @param type $order_by string typ|sortowanie (asc, desc)
     * @param type $limit int limit zwróconych zamowien
     * @return type array z zamowieniami
     */
    public function mget_user_orders($userid, $order_by = null, $limit = null)
    {
        $where = array(
            'id_user' => $userid,
        );
        if (null !== $order_by) {
            $order = explode('|', $order_by);
            $name = $order[0];
            $sorttype = ($order[1] == 'asc' ? 1 : -1);
            $sort = array($name => $sorttype);
        } else {
            $sort = array('data_zamowienia' => -1);
        }

        $options = [
            'sort' => $sort,
            'limit' => $limit
        ];

        $cursor = $this->db->orders->find($where, $options);
        foreach ($cursor as $curs) {
            $res[] = $this->Mgo2array($curs);
        }
        return (!empty($res) ? $res : false);
    }

    public function putFileFromString($json_encoded, $filename)
    {
        $bucket = $this->db->selectGridFSBucket();
        $stream = $bucket->openUploadStream($filename);
        /**
         * @var \MongoDB\BSON\ObjectId $doc
         */
        $doc = $bucket->getFileIdForStream($stream);
        fwrite($stream, $json_encoded);
        fclose($stream);
        return (string) $doc;
    }

    public function getFileContent($id)
    {
        $bucket = $this->db->selectGridFSBucket();
        $stream = $bucket->openDownloadStream(new \MongoDB\BSON\ObjectId($id));
        return stream_get_contents($stream);
    }

    /**
     * Przesyła plika do bazy z tablicy $_FILES lub z pliku na serwerze
     * @param type $filename - STRING nazwa pola input z plikiem
     * @param array $metadata - Tablica asocjacyjna z dodatkowymy metadanymi
     * @return type
     */
    public function upload_file($filename, $metadata = array(), $save = false)
    {
        $grid = $this->db->segetGridFS();
        if ($save) {
            return $grid->storeFile($filename, $metadata);
        } else {
            return $grid->storeUpload($filename, $metadata);
        }
    }

    private function getStram($filename)
    {

    }

    /**
     * Lista plików
     * @return type
     */
    public function files_list()
    {
        $grid = $this->db->getGridFS();
        $cursor = $grid->find();
        foreach ($cursor as $obj) {
            $result[] = array(
                'name' => $obj->getFilename(),
                'id' => $obj->file['_id'],
            );
        }
        return $result;
    }

    /**
     * wyswietla plik
     * @param type $id
     */
    public function get_file($id)
    {
        $grid = $this->db->getGridFS();
        $file = $grid->findOne(array('_id' => new MongoId($id)));
        $filename = $file->getFilename();

        $ext = substr($filename, -3);
        $ct = array(
            'jpg' => 'jpeg',
            'gif' => 'gif',
            'png' => 'png',
        );
        switch ($ext) {
            default:
                header('Content-Type: image/' . $ct[$ext]);
//                header('Content-Disposition: image; filename=' . $filename);
                header('Content-Length: ' . strlen($file->getBytes()));
                echo $file->getBytes();
                break;

//            case 'jpg':
//            case 'png':
//            case 'gif':
            case 'zip':
            case 'rar':
            case 'pdf':
            case 'odt':
            case 'ods':
            case 'odp':
            case 'xls':
            case 'doc':
            case 'ppt':
            case 'docx':
            case 'pptx':
                header('Content-Type: ' . $file->contentType);
                header('Content-Disposition: image; filename=' . $filename);
                header('Content-Transfer-Encoding: binary');
                $cursor = $this->db->fs->chunks->find(array("files_id" => new MongoId($id)), ['sort' => ["n" => 1]]);
                foreach ($cursor as $chunk) {
                    echo $chunk['data']->bin;
                }
                break;
        }
    }

    /**
     * Pobiera plik
     * @param type $id
     */
    public function download_file($id)
    {
        $grid = $this->db->getGridFS();
        $file = $grid->findOne(array('_id' => new MongoId($id)));
        $filename = $file->getFilename();

        $ext = substr($filename, -3);
        switch ($ext) {
            default:
                header('Content-Type: ' . $file->contentType);
                echo $file->getBytes();
                break;

            case 'jpg':
            case 'png':
            case 'gif':
            case 'zip':
            case 'rar':
            case 'pdf':
            case 'odt':
            case 'ods':
            case 'odp':
            case 'xls':
            case 'doc':
            case 'ppt':
            case 'docx':
            case 'pptx':
                header('Content-Type: application/octet-stream');
                header('Content-Disposition: attachment; filename=' . $filename);
                header('Content-Transfer-Encoding: binary');
                $cursor = $this->db->fs->chunks->find(array("files_id" => new MongoId($id)), ['sort' => ["n" => 1]]);
                foreach ($cursor as $chunk) {
                    echo $chunk['data']->bin;
                }
                break;
        }
    }

    /**
     * Usuwa plik
     * @param type $id
     */
    public function delete_file($id)
    {
        $grid = $this->db->getGridFS();
        $grid->delete(new MongoId($id));
    }

    /**
     * Pobiera ostatni dokument z kolekcji
     * @param type $collection
     * @return type
     */
    public function collection_last_record($collection = 'case')
    {
        $cursor = $this->db->$collection->find([], [
            'sort' => [
                'id_ticket' => -1
            ],
            'limit' => 1
        ]);
        foreach ($cursor as $c) {
            $res[] = $this->Mgo2array($c);
        }
        return $res[0] ? $res[0] : false;
    }


    /**
     * Zwraca list lub pojedynczą sprawę
     * @param integer $status
     * @param boolean $user
     * @param integer $id_ticket
     * @param boolean $single
     * @param string $cat
     * @param integer $id_order
     * @return boolean
     */
    public function case_list($status = 'all', $user = true, $id_ticket = false, $single = false, $cat = false, $hash = false, $id_order = false)
    {
        $aWhere = array();
        if ($user) {
            $aWhere['prowadzacy'] = ( string )USER::GetUserID();
        }
        if ($status !== 'all') {
            $aWhere['status_sprawy'] = intval($status);
        }
        if ($id_ticket) {
            $aWhere['id_ticket'] = intval($id_ticket);
        }
        if ($hash) {
            $aWhere['hash'] = $hash;
        }
        if ($cat) {
            $aWhere['kategoria_sprawy'] = $cat;
        }

        if ($id_order) {
            $aWhere['id_zamowienia'] = $id_order;
        }

        $sort = array('id_ticket' => -1);
        $cursor = $this->db->case->find($aWhere, ['sort' => $sort]);
        foreach ($cursor as $case) {
            $res[] = $this->Mgo2array($case);
        }

        if ($res) {
            if (!$single) {
                foreach ($res as $r) {
                    $resArray[$r['id_ticket']] = $r;
                }
                return $resArray;
            } else {
                return $res[0];
            }
        } else {
            return false;
        }
    }

    public function case_list_ajax($status, $category)
    {
        $aWhere['status_sprawy'] = intval($status);
        $aWhere['kategoria_sprawy'] = $category;
        $proj = array(
            'id_ticket' => 1,
            'data_zalozenia' => 1,
            'dane_klienta' => 1,
            'email_klienta' => 1,
            'tytul_sprawy' => 1,
        );
        $cursor = $this->db->case->find($aWhere, $proj);

        foreach ($cursor as $case) {
            $res[] = $this->Mgo2array($case);
        }

        if (!$res) {
            return array();
        }

        foreach ($res as $key => $row) {
            $res[$key]['id'] = $row['id_ticket'];
        }

        return $res;
    }

    public function return_data($id)
    {
        $where = array(
            '_id' => new MongoId($id)
        );
        $cursor = $this->db->returns->findOne($where);
        if ($cursor) {
            return $this->Mgo2array($cursor);
        } else {
            return false;
        }
    }

    public function Aggregate($operators, $collection = 'offers', $show_op = false)
    {
        if ($show_op) {
            Tools::PA($operators, 'Tablica operatorów');
        }
        if ($this->mongoVersion['major'] < 4 and $this->mongoVersion['minor'] < 6) {
            $cursor = $this->db->$collection->aggregate($operators);
            $array = $this->Mgo2array($cursor->toArray());
            $this->normalizeReadDates($array);
            return !empty($array) ? $array : false;

        } else {

            $cursor = $this->db->$collection->aggregate($operators, array('cursor' => array('batchSize' => 101)));

            $ret = $cursor['cursor']['firstBatch'] ? $this->Mgo2array($cursor['cursor']['firstBatch']) : [];
            $this->normalizeReadDates($ret);
            return $ret;
        }
    }

    public function get_invoice($invoice_number)
    {
        $where = array('invoice_number' => $invoice_number);
        $cursor = $this->db->invoices->findOne($where);
        if ($cursor) {
            return $this->Mgo2array($cursor);
        } else {
            return false;
        }
    }

    public function mget_orders_list($fields, $where, $orderby = null, $limit = null, $ret = false): array
    {
        $projection = $this->makeProjection($fields);
        $aWhere = [];
        foreach ($where as $k => $v) {
            switch ($k) {
                default:
                    $aWhere[$k] = $v;
                    break;
            }

        }

        $options = [
            'sort' => ['_id' => 1],
            'limit' => $limit
        ];

        try{
            if (count($projection) === 0) {
                $cursor = $this->handler->{self::$mgoname}->orders->find($aWhere, $options);
            } else {
                $options['projection'] = $projection;
                $cursor = $this->handler->{self::$mgoname}->orders->find($aWhere, $options);
            }
            $out = $cursor->toArray();
        } catch (\Exception $e) {
            $out['error'] = $e->getMessage();
        }

        if ($ret) {
            die(var_dump($out));
        }

        return $out;
    }

    public static function isValidMongoid($mongoid)
    {
        try{
            $new_mongoid = new \MongoDB\BSON\ObjectId($mongoid);
            return $new_mongoid . '' == $mongoid;
        } catch (\Exception $e) {
            return false;
        }
    }

    public static function DateToMongoIdString(DateTimeInterface $date): string
    {
        return dechex($date->getTimestamp()) . "0000000000000000";
    }

    public static function DateToMongoId(DateTimeInterface $date): \MongoDB\BSON\ObjectId
    {
        return new \MongoDB\BSON\ObjectId(self::DateToMongoIdString($date));
    }

}

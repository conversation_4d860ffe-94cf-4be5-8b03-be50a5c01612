                                
                                <form name="deleteform" id="deleteform" action="admin.php?{{$module}}&a=listajax" method="POST">
                                    <input type="hidden" name="redirect" value='{{$redirect}}'>
                                    <div class=''>
                                        <div class='row'>
                                            <div class='col-sm-12 mb-20'>    <h1>{{App::_<PERSON>('Szukaj','Promocje')}}: </h1></div>
                                            <div class='col-md-6'>

                                                <div class="col-md-12 alpha omega">
                                                    <div class="col-md-6 alpha">
                                                        <div class='form-group'>
                                                            <div class="controls">
                                                                <label>{{App::_Lang('Oferta','Promocje')}}</label>
                                                                <select name='oferta' class='form-control' id="oferta" data-rel="chosen">
                                                                    <option value='0'>--</option>
                                                                    <optgroup name="oferty_online" id="oferty_online" label="{{App::_Lang('Oferty przygotowane do lub w emisji','Promocje')}}">
                                                                    {{foreach $oferty as $oferta}}
                                                                        {{if $oferta.status.id eq 6 || $oferta.status.id eq 7}} {{continue}} {{/if}}
                                                                        <option value='{{$oferta.parentid}}' {{if $params.oferta==$oferta.parentid}} selected{{/if}}>{{$oferta.nazwa[$smarty.const.LANG]}} ({{$oferta.status.nazwa}})</option>
                                                                    {{/foreach}}
                                                                    </optgroup>
                                                                    <optgroup name="oferty_offline" id="oferty_offline" label="{{App::_Lang('Oferty poza emisją','Promocje')}}" style="display:none;">
                                                                    {{foreach $oferty as $oferta}}
                                                                        {{if $oferta.status.id eq 4 || $oferta.status.id eq 5}} {{continue}} {{/if}}
                                                                        <option value='{{$oferta.parentid}}' {{if $params.oferta==$oferta.parentid}} selected{{/if}}>{{$oferta.nazwa[$smarty.const.LANG]}} ({{$oferta.status.nazwa}})</option>
                                                                    {{/foreach}}
                                                                    </optgroup>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6 omega">
                                                        <div class='form-group'>
                                                            <div class="controls">
                                                                <label>{{App::_Lang('Status','Calendar')}}</label>
                                                                <select name='status' class='form-control' id="status_filter" data-rel="chosen">
                                                                    <option value=''>{{App::_Lang('Wszystkie','Calendar')}}</option>
                                                                    <option value='-1' {{if $params.status eq '-1'}} selected{{/if}}>{{App::_Lang('Nowy','Calendar')}}</option>
                                                                    <option value='0' {{if $params.status eq '0'}} selected{{/if}}>{{App::_Lang('Nieaktywny','Calendar')}}</option>
                                                                    <option value='1' {{if $params.status eq '1'}} selected{{/if}}>{{App::_Lang('Wygenerowany','Calendar')}}</option>
                                                                    <option value='2' {{if $params.status eq '2'}} selected{{/if}}>{{App::_Lang('Przygotowany do generowania','Calendar')}}</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="col-md-12 alpha omega">
                                                    <div class="col-md-6 alpha">
                                                        <div class='form-group'>
                                                            <div class="controls">
                                                                <label>{{App::_Lang('Nazwa','Calendar')}}</label>
                                                                <input class='form-control' name='nazwa' id="nazwa" type='text' value="{{$params.nazwa}}">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class='col-sm-6 '>
                                                        <div class="controls">
                                                            <label>{{App::_Lang('Pokaż oferty offline','Calendar')}}</label>
                                                            <input class='form-control' name='offline' id="offline" type='checkbox' value="1">
                                                        </div>
                                                    </div>
                                                </div>

                                            </div>

                                            <div class='col-md-6'>
                                                <div class='col-sm-12'>
                                                    <div class='form-group form-inline'>
                                                        <div class="controls">
                                                            <label>{{App::_Lang('Data emisji','Promocje')}}:</label>
                                                            <div class='input-group'>
                                                                <span class="input-group-addon"> {{App::_Lang('od','Promocje')}} </span><input class='form-control' name='dataod' id='dataod' type='text' value="{{$params.data_od}}">
                                                                <span class="input-group-addon"> {{App::_Lang('do','Promocje')}} </span><input class='form-control' name='datado' id='datado' type='text'value="{{$params.data_do}}">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class='col-sm-12 form-group' >
                                                    <div class='row'>
                                                    <div class='col-sm-6 '>
                                                        <div class="controls">
                                                            <label>{{App::_Lang('Akcept','Promocje')}}</label>
                                                            <select name='akceptacja' class='form-control' id="akceptacja" >
                                                                <option value=''>--</option>                   
                                                                <option value='1' {{if $params.akceptacja=='1'}} selected{{/if}}>{{App::_Lang('zaakceptowane','Promocje')}}</option>
                                                                <option value='0' {{if $params.akceptacja=='0'}} selected{{/if}}>{{App::_Lang('brak','Promocje')}}</option>

                                                            </select>
                                                        </div>
                                                    </div>

                                                    <div class='col-sm-6'>

                                                        <div class="controls">
                                                            <label>{{App::_Lang('Status emisji','Promocje')}}</label>
                                                            <select name='status' class='form-control' id="status">
                                                                <option value=''>--</option>
                                                                {{foreach $params.statusy as $status}}
                                                                <option value='{{$status.id}}' {{if $params.status===$status.id}} selected{{/if}}>{{$status.nazwa}} ({{$status.id}})</option>
                                                                {{/foreach}}
                                                            </select>
                                                        </div>
                                                    </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                    <div class='col-sm-12'>
                                        <input type='button' class='btn btn-default pull-right' value='{{App::_Lang('Szukaj','Promocje')}}' id="btnSearchStart">
                                    </div>
                                    </div>

                        <table  class="table table-striped table-bordered bootstrap-datatable datatable" >
                            <thead>
                                <tr>
                                    <th class="col-sm-1">#</th>
                                    <th class="col-sm-2">{{App::_Lang('Oferta','Calendar')}}</th>
                                    <th class="col-sm-2">{{App::_Lang('Produkt','Calendar')}}</th>
                                    <th class="col-sm-1">{{App::_Lang('Data sprzedaży od','Calendar')}}</th>
                                    <th class="col-sm-1">{{App::_Lang('Wariant','Calendar')}}</th>
                                    <th class="col-sm-1">{{App::_Lang('Status','Calendar')}}</th>
                                    <th class="col-sm-1">{{App::_Lang('Wpisy kalendarza','Calendar')}}</th>
                                    <th class="col-sm-2">{{App::_Lang('Akcje','Calendar')}}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="8" class="dataTables_empty">{{App::_Lang('Wczytuję dane...')}}</td>
                                </tr>
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="10">
{{*                                        <button class="btn btn-danger btn-xs" id="delbtn" name="del">{{App::_Lang('Usuń zaznaczone','Promocje')}}</button>*}}
                                    </th>
                                </tr>
                            </tfoot>
                        </table>

                        </form>
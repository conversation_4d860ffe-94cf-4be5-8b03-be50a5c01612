{{$header}}

<body>
    {{$top}}

    <div class="container">
        <div class="row">

            {{$adminnav}}

            <!-- start: Content -->
            <div id="content" class="col-sm-11">
                <div class="row">		
                    <div class="col-lg-12">
                        <form class="form-horizontal" name="edit" id="edit" data-todo="{{$todo}}" action="admin.php?pricescalendar&a=save" method="POST" enctype="multipart/form-data">
                            <div class="box" id="section_main">
                                <div class="box-header" data-original-title>
                                    <h2><i class="fa fa-list"></i><span class="break"></span>{{if $todo eq 'add'}}{{App::_Lang('Dodaj kalendarz','Calendar')}}{{else}}{{App::_Lang('Edycja kuponu','Promocje')}}{{/if}}</h2>
                                    <div class="pull-right submenubox">

                                        <span class="break"></span>

                                            <a href="{{if $smarty.const.BACKLINK}}{{$smarty.const.BACKLINK}}{{else}}admin.php?{{$smarty.const.MODULE}}&a=list{{/if}}" class="btn btn-info btn-sm"><i class="fa fa-arrow-circle-o-left"></i> {{App::_Lang('powrót','Promocje')}}</a>
                                        {{if $buttons.zapisz}}
                                            <button type="submit" class="btn btn-success btn-sm" disabled><i class="fa fa-plus-square"></i> {{App::_Lang('Zapisz','Promocje')}}</button>
                                        {{/if}}

                                    </div>
                                </div>

                                <div class="box-content-noafter">
                                    <input type="hidden" name='section' value='{{$section}}'>
                                    <input type="hidden" name='cid' value='{{$cid}}'>
                                    <input type="hidden" name='todo' value='{{$todo}}' id="todo">
                                    <h2>{{App::_Lang('Dane podstawowe','Promocje')}}</h2>
                                    <div class="form-group">
                                        <label class="col-md-2 control-label" for="daty">{{App::_Lang('W sprzedaży','Promocje')}}*</label>
                                        <div class="col-md-5">
                                            <div class="input-group">
                                                <span class="input-group-addon"> {{App::_Lang('od','Promocje')}} </span><input class='form-control' name='sell_date_from' id='dataod' type='text' value="{{$postback.sell_date_from}}" required>
                                                <span class="input-group-addon"> {{App::_Lang('do','Promocje')}} </span><input class='form-control' name='sell_date_to' id='datado' type='text' value="{{$postback.sell_date_to}}" required>
                                            </div>
                                        </div>
                                        <div class="col-md-5 form-control-static">{{App::_Lang('Czas obowiązywania kuponów. Data końcowa nie jest konieczna')}}</div>
                                    </div>

                                    <div class="form-group" id='ograniczenie-ilosci-row'>
                                        <label class="col-md-2 control-label" for="nieograniczone">{{App::_Lang('Pokaż w sklepie','Promocje')}}</label>
                                        <div class="col-md-5">
                                            <input type="checkbox" name="active" id="active" {{if $postback.status eq 1}} checked{{/if}} value='1' class="form-control">
                                        </div>
                                        <div class="col-md-5 form-control-static">{{App::_Lang('Promocja zostanie aktywowana i pojawi się w sklepie w okresie sprzedaży','Promocje')}}</div>
                                    </div>


                                    <div class="form-group">
                                        <label class="col-md-2 control-label" for="oferta">{{App::_Lang('Oferta','Promocje')}}*</label>
                                        <div class="col-md-5">
                                            <select class="form-control" required name="id_offer" id="oferta">
                                                <option value="">{{App::_Lang('wybierz ofertę której dotyczy emisja','Promocje')}}</option>
                                                    {{foreach $oferty as $oferta}}
                                                    <option value="{{$oferta.parentid}}" {{if $postback.id_offer eq $oferta.parentid}}selected{{/if}}>{{$oferta.nazwa[$smarty.const.LANG]}}</option>
                                                    {{/foreach}}
                                            </select>
                                        </div>
                                        <div class="col-md-5 form-control-static">{{App::_Lang('Oferta','Promocje')}}</div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="col-md-2 control-label" for="typ-produktu">{{App::_Lang('Typ  produktu','Promocje')}}*</label>
                                        <div class="col-md-5">
                                            <select class="form-control" required name="ptype" id="typ-produktu" {{if $postback.przeznaczenie_kuponu eq 'OPEN'}}disabled{{/if}}>
                                                    <option value=''>{{App::_Lang('wybierz typ produktu')}}</option>
                                                    <option value="karnety" {{if $postback.ptype eq 'karnety'}}selected{{/if}}>{{App::_Lang('Karnety','Promocje')}}</option>
                                                    <option value="karty" {{if $postback.ptype eq 'karty'}}selected{{/if}}>{{App::_Lang('Karty','Promocje')}}</option>
                                                    <option value="vouchery" {{if $postback.ptype eq 'vouchery'}}selected{{/if}}>{{App::_Lang('Vouchery','Promocje')}}</option>
                                            </select>
                                        </div>
                                        <div class="col-md-5 form-control-static">{{App::_Lang('Typ produktu','Promocje')}}</div>
                                    </div>
                                    
                                    <div class="form-group" id="div-produkt">
                                        <label class="col-md-2 control-label" for="produkt">{{App::_Lang('Wybierz produkt','Promocje')}}</label>
                                        <div class="col-md-5">
                                            <select class="form-control" data-rel='chosen' name="id_product" id="produkt">
                                                {{foreach $proferty as $prod}}
                                                    <option value='{{$prod.id}}' {{if $postback.id_product eq $prod.id}}selected{{/if}}>{{$prod.nazwa}}</option>
                                                {{/foreach}}
                                            </select>
                                        </div>
                                        <div class="col-md-5 form-control-static">{{App::_Lang('Wybierz produkt danego typu','Promocje')}}</div>
                                    </div>

                                    <div class="form-group" id="div-wariant">
                                        <label class="col-md-2 control-label" for="wariant">{{App::_Lang('Wybierz wariant','Promocje')}}</label>
                                        <div class="col-md-5">
                                            <select class="form-control" name="id_variant" id="wariant">
                                                {{foreach $variants as $prod}}
                                                    <option value='{{$prod.bsid}}' {{if $postback.id_variant eq $prod.bsid}}selected{{/if}} data-price="{{$prod.cena}}">{{$prod.nazwa}}</option>
                                                {{/foreach}}
                                            </select>
                                        </div>
                                        <div class="col-md-5 form-control-static">{{App::_Lang('Wybierz wariant produktu','Promocje')}}</div>
                                    </div>

                                    <div class="form-group" id="div-cena_bazowa">
                                        <label class="col-md-2 control-label" for="base_price">{{App::_Lang('Cena podstawowa','Promocje')}}</label>
                                        <div class="col-md-5">
                                            <span id="base_price">
                                                {{foreach $variants as $prod}}
                                                    {{if $postback.id_variant eq $prod.bsid}}{{$prod.cena}}{{/if}}
                                                {{/foreach}}
                                            </span>
                                        </div>
                                        <div class="col-md-5 form-control-static">{{App::_Lang('Cena podstawowa wariantu','Promocje')}}</div>
                                    </div>

                                    <div class="form-group" id="div-cena-oferty">
                                        <label class="col-md-2 control-label" for="actual_price">{{App::_Lang('Cena promocyjna','Promocje')}}</label>
                                        <div class="col-md-5">
                                            <input  type="text" class="form-control" name="actual_price" id="actual_price" value="{{$postback.price}}">
                                        </div>
                                        <div class="col-md-5 form-control-static">{{App::_Lang('Cena w danym okresie dla wariantu','Promocje')}}</div>
                                    </div>

                                    
                                    <div class="form-group" id='ograniczenie-ilosci-row'>
                                        <label class="col-md-2 control-label" for="nieograniczone">{{App::_Lang('Nieograniczona ilość kuponów','Promocje')}}</label>
                                        <div class="col-md-5">
                                            <input type="checkbox" name="nieograniczone" id="nieograniczone" {{if $postback.limit eq -1}} checked{{/if}} value='1' class="form-control">
                                        </div>
                                        <div class="col-md-5 form-control-static">{{App::_Lang('W przypadku kuponów o numeracji zbiorczej w puli jest ich nieograniczona ilość','Promocje')}}</div>
                                    </div>

                                    <div class="form-group" id='wielkosc-emisji-row' {{if $postback.limit eq -1}}style='display:none;'{{/if}}>
                                        <label class="col-md-2 control-label" for="volumen">{{App::_Lang('Wielkość emisji','Promocje')}}</label>
                                        <div class="col-md-5">
                                            <input type="text" id="volumen" required name="limit" value="{{$postback.limit}}" class="form-control" data-filter="int" data-parb="razy">
                                        </div>
                                        <div class="col-md-5 form-control-static">{{App::_Lang('Ilość emitowanych kuponów. Konieczne przy numeracji jednostkowej','Promocje')}}</div>
                                    </div>

                                </div>

                                {{* FORMULARZ KONFIGURACJI KARNETU *}}
                                <div class="panel panel-default" id="pass-configuration-panel">
                                    <div class="panel-heading">
                                        <h3 class="panel-title">{{App::_Lang('Konfiguracja karnetu - okresy obowiązywania')}}</h3>
                                    </div>
                                    <div class="panel-body">
                                        <!-- Date Configuration Type Selection -->
                                        <div class="form-group">
                                            <label class="col-md-2 control-label">{{App::_Lang('Typ konfiguracji dat')}}</label>
                                            <div class="col-md-5">
                                                <div class="radio">
                                                    <label>
                                                        <input type="radio" name="configuration[pass_date_type]" id="pass_date_type_specific" value="specific_dates" {{if $postback.configuration.pass_date_type eq 'specific_dates' || !$postback.configuration.pass_date_type}}checked{{/if}}>
                                                        {{App::_Lang('Konkretne daty')}}
                                                    </label>
                                                </div>
                                                <div class="radio">
                                                    <label>
                                                        <input type="radio" name="configuration[pass_date_type]" id="pass_date_type_weekdays" value="weekdays" {{if $postback.configuration.pass_date_type eq 'weekdays'}}checked{{/if}}>
                                                        {{App::_Lang('Dni tygodnia')}}
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-5 form-control-static">{{App::_Lang('Wybierz sposób konfiguracji dat obowiązywania karnetu')}}</div>
                                        </div>

                                        <!-- Specific Dates Configuration -->
                                        <div id="specific-dates-section" {{if $postback.configuration.pass_date_type eq 'weekdays'}}style="display:none;"{{/if}}>
                                            <div class="form-group">
                                                <label class="col-md-2 control-label">{{App::_Lang('Konkretne daty')}}</label>
                                                <div class="col-md-10">
                                                    <div id="specific-dates-container">
                                                        {{if $postback.configuration.pass_specific_dates}}
                                                            {{foreach $postback.configuration.pass_specific_dates as $index => $date_config}}
                                                                <div class="specific-date-row" data-index="{{$index}}">
                                                                    <div class="row mb-10">
                                                                        <div class="col-md-4">
                                                                            <div class="input-group">
                                                                                <span class="input-group-addon">{{App::_Lang('Data')}}</span>
                                                                                <input type="date" class="form-control specific-date-input" name="configuration[pass_specific_dates][{{$index}}][date]" value="{{$date_config.date}}" required>
                                                                            </div>
                                                                        </div>
                                                                        <div class="col-md-3">
                                                                            <div class="input-group">
                                                                                <span class="input-group-addon">{{App::_Lang('Od')}}</span>
                                                                                <input type="time" class="form-control" name="configuration[pass_specific_dates][{{$index}}][time_from]" value="{{$date_config.time_from}}" required>
                                                                            </div>
                                                                        </div>
                                                                        <div class="col-md-3">
                                                                            <div class="input-group">
                                                                                <span class="input-group-addon">{{App::_Lang('Do')}}</span>
                                                                                <input type="time" class="form-control" name="configuration[pass_specific_dates][{{$index}}][time_to]" value="{{$date_config.time_to}}" required>
                                                                            </div>
                                                                        </div>
                                                                        <div class="col-md-2">
                                                                            <button type="button" class="btn btn-danger btn-sm remove-specific-date" {{if $index eq 0}}style="display:none;"{{/if}}>
                                                                                <i class="fa fa-minus"></i> {{App::_Lang('Usuń')}}
                                                                            </button>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            {{/foreach}}
                                                        {{else}}
                                                            <div class="specific-date-row" data-index="0">
                                                                <div class="row mb-10">
                                                                    <div class="col-md-4">
                                                                        <div class="input-group">
                                                                            <span class="input-group-addon">{{App::_Lang('Data')}}</span>
                                                                            <input type="date" class="form-control specific-date-input" name="configuration[pass_specific_dates][0][date]]">
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-md-3">
                                                                        <div class="input-group">
                                                                            <span class="input-group-addon">{{App::_Lang('Od')}}</span>
                                                                            <input type="time" class="form-control" name="configuration[pass_specific_dates][0][time_from]]" value="09:00">
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-md-3">
                                                                        <div class="input-group">
                                                                            <span class="input-group-addon">{{App::_Lang('Do')}}</span>
                                                                            <input type="time" class="form-control" name="configuration[pass_specific_dates][0][time_to]]" value="17:00">
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-md-2">
                                                                        <button type="button" class="btn btn-danger btn-sm remove-specific-date" style="display:none;">
                                                                            <i class="fa fa-minus"></i> {{App::_Lang('Usuń')}}
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        {{/if}}
                                                    </div>
                                                    <div class="row mt-10">
                                                        <div class="col-md-12">
                                                            <button type="button" class="btn btn-success btn-sm" id="add-specific-date">
                                                                <i class="fa fa-plus"></i> {{App::_Lang('Dodaj datę')}}
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Weekdays Configuration -->
                                        <div id="weekdays-section" {{if $postback.configuration.pass_date_type ne 'weekdays'}}style="display:none;"{{/if}}>
                                            <div class="form-group">
                                                <label class="col-md-2 control-label">{{App::_Lang('Dni tygodnia')}}</label>
                                                <div class="col-md-10">
                                                    <div id="weekdays-container">
                                                        {{assign var="weekdays" value=['monday' => 'Poniedziałek', 'tuesday' => 'Wtorek', 'wednesday' => 'Środa', 'thursday' => 'Czwartek', 'friday' => 'Piątek', 'saturday' => 'Sobota', 'sunday' => 'Niedziela']}}
                                                        {{foreach $weekdays as $day_key => $day_name}}
                                                            <div class="weekday-row mb-15" data-day="{{$day_key}}">
                                                                <div class="row">
                                                                    <div class="col-md-2">
                                                                        <div class="checkbox">
                                                                            <label>
                                                                                <input type="checkbox" name="configuration[pass_weekdays][{{$day_key}}][enabled]" value="1" class="weekday-checkbox" {{if $postback.configuration.pass_weekdays[$day_key].enabled}}checked{{/if}}>
                                                                                {{App::_Lang($day_name)}}
                                                                            </label>
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-md-4 weekday-times" {{if !$postback.configuration.pass_weekdays[$day_key].enabled}}style="display:none;"{{/if}}>
                                                                        <div class="input-group">
                                                                            <span class="input-group-addon">{{App::_Lang('Od')}}</span>
                                                                            <input type="time" class="form-control" name="configuration[pass_weekdays][{{$day_key}}][time_from]]" value="{{if $postback.configuration.pass_weekdays[$day_key].time_from}}{{$postback.configuration.pass_weekdays[$day_key].time_from}}{{else}}09:00{{/if}}">
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-md-4 weekday-times" {{if !$postback.configuration.pass_weekdays[$day_key].enabled}}style="display:none;"{{/if}}>
                                                                        <div class="input-group">
                                                                            <span class="input-group-addon">{{App::_Lang('Do')}}</span>
                                                                            <input type="time" class="form-control" name="configuration[pass_weekdays][{{$day_key}}][time_to]]" value="{{if $postback.configuration.pass_weekdays[$day_key].time_to}}{{$postback.configuration.pass_weekdays[$day_key].time_to}}{{else}}17:00{{/if}}">
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        {{/foreach}}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {{if $generated_calendar && count($generated_calendar) > 0}}
                                <div class="box-content">
                                    <h3>{{App::_Lang('Wygenerowany kalendarz cen')}}</h3>
                                    <div class="table-responsive">
                                        <table class="table table-striped table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>{{App::_Lang('Data i godzina rozpoczęcia')}}</th>
                                                    <th>{{App::_Lang('Data i godzina zakończenia')}}</th>
                                                    <th>{{App::_Lang('Cena')}}</th>
                                                    <th>{{App::_Lang('Status')}}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {{foreach $generated_calendar as $entry}}
                                                <tr>
                                                    <td>{{$entry.datetime_start}}</td>
                                                    <td>{{$entry.datetime_stop}}</td>
                                                    <td>{{$entry.price}} PLN</td>
                                                    <td>
                                                        {{if $entry.status == 1}}
                                                            <span class="label label-success">{{App::_Lang('Aktywny')}}</span>
                                                        {{else}}
                                                            <span class="label label-default">{{App::_Lang('Nieaktywny')}}</span>
                                                        {{/if}}
                                                    </td>
                                                </tr>
                                                {{/foreach}}
                                            </tbody>
                                        </table>
                                    </div>
                                    <p class="text-muted">{{App::_Lang('Łącznie wpisów')}} {{count($generated_calendar)}}</p>
                                </div>
                                {{/if}}

                                <div class="box-footer mt-20">
                                    <div class="pull-right submenubox">
                                        <span class="break"></span>
                                            <a href="{{if $smarty.const.BACKLINK}}{{$smarty.const.BACKLINK}}{{else}}admin.php?{{$smarty.const.MODULE}}&a=list{{/if}}" class="btn btn-info btn-sm"><i class="fa fa-arrow-circle-o-left"></i> {{App::_Lang('powrót','Promocje')}}</a>
                                            {{if $todo eq 'edit'}}
                                            <a href="admin.php?pricescalendar&a=delete&id={{$cid}}" class="btn btn-danger btn-sm" onclick="return confirm('{{App::_Lang('Czy chcesz usunąć cały kalendarz wraz z konfiguracją? Ta operacja jest nieodwracalna.','Promocje')}}')"><i class="fa fa-trash"></i> {{App::_Lang('Usuń','Promocje')}}</a>
                                                {{if $generated_calendar && count($generated_calendar) > 0}}
                                                <a href="admin.php?pricescalendar&a=clear&id={{$cid}}" class="btn btn-warning btn-sm" onclick="return confirm('{{App::_Lang('Czy chcesz usunąć tylko wpisy kalendarza? Konfiguracja zostanie zachowana.','Promocje')}}')"><i class="fa fa-eraser"></i> {{App::_Lang('Wyczyść kalendarz','Promocje')}}</a>
                                                {{/if}}
                                                <a href="admin.php?pricescalendar&a=generate&id={{$cid}}" class="btn btn-primary btn-sm" onclick="return confirm('{{App::_Lang('Czy chcesz wygenerować kalendarz cen? Istniejące wpisy zostaną usunięte.','Promocje')}}')"><i class="fa fa-calendar"></i> {{App::_Lang('Generuj kalendarz','Promocje')}}</a>
                                                <button type="button" class="btn btn-success btn-sm" id="save_and_generate" data-id="{{$cid}}"><i class="fa fa-save"></i> {{App::_Lang('Zapisz i generuj','Promocje')}}</button>
                                            {{/if}}
                                             <button type="submit" class="btn btn-success btn-sm"><i class="fa fa-plus-square"></i> {{App::_Lang('Zapisz','Promocje')}}</button>
                                    </div>
                                </div>
                            </div>
                            
                        </form>
                    </div>
                </div><!--/col-->
            </div><!--/row-->
        </div>
        <!-- end: Content -->
    </div><!--/row-->
    <!--</div>/container-->
    <div class="clearfix"></div>
       <!-- Product Info Modal -->
    {{$footer}}

    {{include file='partials/scripts.tpl'}}

    <style>
        #pass-configuration-panel .panel-body {
            padding: 20px;
        }

        .specific-date-row, .weekday-row {
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }

        .specific-date-row:last-child, .weekday-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .weekday-times {
            transition: all 0.3s ease;
        }

        .remove-specific-date {
            margin-top: 0;
        }

        #pass-configuration-panel .radio {
            margin-bottom: 10px;
        }

        #pass-configuration-panel .checkbox {
            margin-bottom: 0;
        }

        .weekday-row .checkbox label {
            font-weight: 600;
            min-width: 120px;
        }

        #add-specific-date {
            margin-top: 10px;
        }

        .specific-date-input {
            min-width: 150px;
        }
    </style>

    {{if $viewmode eq 'view'}}

    {{else}}

        {{include file='modules/pricescalendar/scripts-edit.tpl'}}

        {{include file='partials/noty.tpl'}}

    {{/if}}

    <!-- end: JavaScript-->
</body>
</html>
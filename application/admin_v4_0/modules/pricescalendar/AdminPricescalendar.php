<?php

class AdminPricescalendar extends AdminModule
{
    protected $headerparams = [];
    protected $topparams = [];
    protected $adminnavparams = [];
    protected $footerparams = [];
    protected $offers = [];
    protected $offersObjects = [];
    protected $en = null;
    protected $partners = null;

    public function __construct($app)
    {
        $this->app = &$app;
        $this->en = Database::activate();
        Tools::activate($this->app->urlparam);

        switch ($this->app->urlparam['a']) {
            case 'list':
                $this->return = $this->_r('list');
                break;

            case 'listajax':
                $this->return = $this->_r('AjaxList');
                break;

            case 'add':
                $this->return = $this->_r('AddCalendar');
                break;

            case 'edit':
                $this->return = $this->_r('EditCalendar');
                break;

            case 'save':
                $this->return = $this->_r('Save');
                break;

            case 'generate':
                $this->return = $this->_r('GenerateCalendar');
                break;

            case 'delete':
                $this->return = $this->_r('DeleteCalendar');
                break;

            case 'clear':
                $this->return = $this->_r('ClearCalendar');
                break;

            case 'duplicate':
                $this->return = $this->_r('DuplicateCalendar');
                break;

            case 'opl':
                $offerid = $_REQUEST['offerid'];
                $ptype = $_REQUEST['ptype'];
                $this->ajaxGetOfferProductsList($offerid, $ptype);
                break;

            case 'pvl':
                $offerid = $_REQUEST['offerid'];
                $ptype = $_REQUEST['ptype'];
                $product = $_REQUEST['product'];
                $this->ajaxGetProductVariantsList($offerid, $ptype, $product);
                break;

            default:
                $this->return = $this->_r('DisplayListCoupons');
                break;
        }
    }

    public function save()
    {
        $pc = null;
        switch ($_POST['todo']) {
            case 'add':
                $pc = $this->create($_POST);
                break;
            case 'edit':
                $pc = $this->update($_POST, $_POST['cid']);
                break;
        }

        redirect(SITE_URL . 'admin.php?pricescalendar&a=edit&id=' . $pc->id);
    }

    /**
     * Generuje kalendarz cen dla istniejącego kalendarza
     */
    public function GenerateCalendar()
    {
        $id = $this->app->urlparam['id'] ?? null;
        if (!$id) {
            $_SESSION['notify'] = 'error';
            $_SESSION['notifytext'] = 'Brak ID kalendarza';
            redirect(SITE_URL . 'admin.php?pricescalendar&a=list');
            return;
        }

        $pc = new \App\Libs\Models\PriceCalendar();
        $pc->findOne($id);

        if (!$pc->id && !$pc->attributes['id']) {
            $_SESSION['notify'] = 'error';
            $_SESSION['notifytext'] = 'Kalendarz nie został znaleziony';
            redirect(SITE_URL . 'admin.php?pricescalendar&a=list');
            return;
        }

        $result = $pc->generateCalendar();

        if ($result) {
            $calendar = $pc->getCalendar();
            $_SESSION['notify'] = 'success';
            $_SESSION['notifytext'] = 'Kalendarz został wygenerowany pomyślnie. Utworzono ' . count($calendar) . ' wpisów.';
        } else {
            $_SESSION['notify'] = 'error';
            $_SESSION['notifytext'] = 'Błąd generowania kalendarza: ' . $pc->error;
        }

        redirect(SITE_URL . 'admin.php?pricescalendar&a=edit&id=' . $id);
    }

    /**
     * Usuwa kalendarz cen wraz z konfiguracją
     */
    public function DeleteCalendar()
    {
        $id = $this->app->urlparam['id'] ?? null;
        if (!$id) {
            $_SESSION['notify'] = 'error';
            $_SESSION['notifytext'] = 'Brak ID kalendarza';
            redirect(SITE_URL . 'admin.php?pricescalendar&a=list');
            return;
        }

        $pc = new \App\Libs\Models\PriceCalendar();
        $pc->findOne($id);

        if (!$pc->id && !$pc->attributes['id']) {
            $_SESSION['notify'] = 'error';
            $_SESSION['notifytext'] = 'Kalendarz nie został znaleziony';
            redirect(SITE_URL . 'admin.php?pricescalendar&a=list');
            return;
        }

        $result = $pc->deleteCalendar();

        if ($result) {
            $_SESSION['notify'] = 'success';
            $_SESSION['notifytext'] = 'Kalendarz został usunięty pomyślnie wraz z wszystkimi wpisami.';
        } else {
            $_SESSION['notify'] = 'error';
            $_SESSION['notifytext'] = 'Błąd usuwania kalendarza: ' . $pc->error;
        }

        redirect(SITE_URL . 'admin.php?pricescalendar&a=list');
    }

    /**
     * Usuwa tylko wpisy kalendarza (bez usuwania konfiguracji)
     */
    public function ClearCalendar()
    {
        $id = $this->app->urlparam['id'] ?? null;
        if (!$id) {
            $_SESSION['notify'] = 'error';
            $_SESSION['notifytext'] = 'Brak ID kalendarza';
            redirect(SITE_URL . 'admin.php?pricescalendar&a=edit&id=' . $id);
            return;
        }

        $pc = new \App\Libs\Models\PriceCalendar();
        $pc->findOne($id);

        if (!$pc->id && !$pc->attributes['id']) {
            $_SESSION['notify'] = 'error';
            $_SESSION['notifytext'] = 'Kalendarz nie został znaleziony';
            redirect(SITE_URL . 'admin.php?pricescalendar&a=edit&id=' . $id);
            return;
        }

        $result = $pc->clearCalendarOnly();

        if ($result) {
            $_SESSION['notify'] = 'success';
            $_SESSION['notifytext'] = 'Wpisy kalendarza zostały usunięte pomyślnie.';
        } else {
            $_SESSION['notify'] = 'error';
            $_SESSION['notifytext'] = 'Błąd usuwania wpisów kalendarza: ' . $pc->error;
        }

        redirect(SITE_URL . 'admin.php?pricescalendar&a=edit&id=' . $id);
    }

    /**
     * Duplikuje kalendarz cen (kopiuje konfigurację, ustawia status na -1, nie generuje kalendarza)
     */
    public function DuplicateCalendar()
    {
        $id = $this->app->urlparam['id'] ?? null;
        if (!$id) {
            $_SESSION['notify'] = 'error';
            $_SESSION['notifytext'] = 'Brak ID kalendarza';
            redirect(SITE_URL . 'admin.php?pricescalendar&a=list');
            return;
        }

        $sourceCalendar = new \App\Libs\Models\PriceCalendar();
        $sourceCalendar->findOne($id);

        if (!$sourceCalendar->id && !$sourceCalendar->attributes['id']) {
            $_SESSION['notify'] = 'error';
            $_SESSION['notifytext'] = 'Kalendarz nie został znaleziony';
            redirect(SITE_URL . 'admin.php?pricescalendar&a=list');
            return;
        }

        try {
            $newCalendar = new \App\Libs\Models\PriceCalendar();

            $attributes = $sourceCalendar->asArray();
            $attributes['status'] = -1;
            unset($attributes['id'], $attributes['ts']);

            $newCalendar->setConfiguration($sourceCalendar->getConfiguration());

            foreach ($attributes as $key => $value) {
                if ($key !== 'configuration') { // configuration jest już ustawiona
                    $newCalendar->$key = $value;
                }
            }
            
            $newId = $newCalendar->save();
            
            if ($newId) {
                $_SESSION['notify'] = 'success';
                $_SESSION['notifytext'] = 'Kalendarz został zduplikowany pomyślnie. Status ustawiony na "Nowy".';
                redirect(SITE_URL . 'admin.php?pricescalendar&a=edit&id=' . $newId);
            } else {
                $_SESSION['notify'] = 'error';
                $_SESSION['notifytext'] = 'Błąd podczas duplikowania kalendarza.';
                redirect(SITE_URL . 'admin.php?pricescalendar&a=list');
            }
        } catch (Exception $e) {
            $_SESSION['notify'] = 'error';
            $_SESSION['notifytext'] = 'Błąd duplikowania kalendarza: ' . $e->getMessage();
            redirect(SITE_URL . 'admin.php?pricescalendar&a=list');
        }
    }

    protected function create($data): \App\Libs\Models\PriceCalendar
    {
        $pc = new \App\Libs\Models\PriceCalendar();
        $pc->sell_date_from = (new \DateTime($data['sell_date_from']))->format('Y-m-d H:i');
        $pc->sell_date_to = (new \DateTime($data['sell_date_to']))->format('Y-m-d H:i');
        $pc->id_offer = $data['id_offer'];
        $pc->ptype = $data['ptype'];
        $pc->id_product = $data['id_product'];
        $pc->id_variant = $data['id_variant'];
        $pc->price = number_format($data['actual_price'] * 100 / 100, 2, '.', '');
        $pc->limit = (int)($data['nieograniczone'] ?? 0) === 1 ? -1 : $data['limit'];
        $pc->left = $pc->limit;
        $pc->setConfiguration($data['configuration']);
        $pc->status = -1;
        $pc->save();
        return $pc;
    }


    protected function update($data, $id): \App\Libs\Models\PriceCalendar
    {
        $pc = new \App\Libs\Models\PriceCalendar();
        $pc->findOne($id);
        $pc->sell_date_from = (new \DateTime($data['sell_date_from']))->format('Y-m-d H:i');
        $pc->sell_date_to = (new \DateTime($data['sell_date_to']))->format('Y-m-d H:i');
        $pc->id_offer = $data['id_offer'];
        $pc->ptype = $data['ptype'];
        $pc->id_product = $data['id_product'];
        $pc->id_variant = $data['id_variant'];
        $pc->price = number_format($data['actual_price'] * 100 / 100, 2, '.', '');
        $pc->limit = (int)($data['nieograniczone'] ?? 0) === 1 ? -1 : $data['limit'];
        $pc->left = $pc->limit;
        $pc->setConfiguration($data['configuration']);
        $pc->status = $data['active'] ?? -1;
        $pc->save();
        return $pc;
    }

    public function list()
    {
        $this->headerparams['meta']['title'] = $this->app->Lang('Lista cen',
                'Calendar') . " | ADMIN " . $this->headerparams['meta']['title'];
        Tools::activate($this->app->urlparam);
        $redirect = urldecode($_GET['redirect']);
        if ($redirect == '') {
            $redirect = SITE_URL . 'admin.php?pricescalendar&a=list';
            $redirect = urlencode($redirect);
        }
        $params['offers'] = array_merge(Tools::GetPartners(1), Tools::GetPartners(6));
        $params['statusy'] = array(
            array('id' => -1, 'nazwa' => 'nowa'),
            array('id' => 0, 'nazwa' => 'zakończona'),
            array('id' => 1, 'nazwa' => 'aktywna')
        );
        $params['data_od'] = $this->app->urlparam['dataod'];
        $params['data_do'] = $this->app->urlparam['datado'];
        $params['status'] = $this->app->urlparam['status'];
        $params['oferta'] = $this->app->urlparam['oferta'];
        $params['nazwa'] = $this->app->urlparam['nazwa'];
        if ($_SESSION['notify']) {
            $this->app->ADD($_SESSION['notify'], true);
            $this->app->ADD('notifytext', $_SESSION['notifytext']);
            unset($_SESSION['notify']);
            unset($_SESSION['notifytext']);
        }
        $mgo = Mymongo::activate();
        $oferty = $mgo->mget_accepted_offers_list(true, true);
        $this->app->ADD('oferty', $oferty);
        $this->app->ADD('params', $params);
        $this->app->AddDecoratorData('module', MODULE);
        $this->adminnavparams['selected'] = 'calendar.list';
        $this->app->ADD('tablescript', 'modules/pricescalendar/calendar_table.tpl');
        $this->app->ADD('headertitle', 'Kalendarz');
        $this->app->AddDecoratorModule('moduledata', "modules/" . MODULE . "/list_calendar.tpl");
        $this->makeWidgets();

        $this->app->SetTemplate("modules/" . MODULE . "/page.tpl");
        $this->makeWidgets();
    }

    public function AjaxList()
    {
        // Parametry DataTables
        $draw = (int)($_REQUEST['draw'] ?? 1);
        $start = (int)($_REQUEST['start'] ?? 0);
        $length = (int)($_REQUEST['length'] ?? 10);
        $orderColumn = (int)($_REQUEST['order'][0]['column'] ?? 0);
        $orderDir = $_REQUEST['order'][0]['dir'] ?? 'desc';

        // Parametry filtrowania
        $dataod = $_REQUEST['dataod'] ?? '';
        $datado = $_REQUEST['datado'] ?? '';
        $nazwa = $_REQUEST['nazwa'] ?? '';
        $oferta = $_REQUEST['oferta'] ?? '';
        $status = $_REQUEST['status'] ?? '';

        // Mapowanie kolumn
        $columns = ['id', 'id_offer', 'id_product', 'sell_date_from', 'id_variant', 'status', 'status', 'id'];
        $orderBy = $columns[$orderColumn] ?? 'id';

        $db = Database::activate()->gethandler();
        $whereConditions = [];
        $params = [];
        $paramTypes = '';

        if (!empty($dataod)) {
            $whereConditions[] = "sell_date_from >= ?";
            $params[] = $dataod . ' 00:00:00';
            $paramTypes .= 's';
        }

        if (!empty($datado)) {
            $whereConditions[] = "sell_date_to <= ?";
            $params[] = $datado . ' 23:59:59';
            $paramTypes .= 's';
        }

        if (!empty($oferta)) {
            $whereConditions[] = "id_offer = ?";
            $params[] = $oferta;
            $paramTypes .= 's';
        }

        if ($status !== '') {
            $whereConditions[] = "status = ?";
            $params[] = intval($status);
            $paramTypes .= 'i';
        }

        $whereClause = '';
        if (!empty($whereConditions)) {
            $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
        }

        $totalQuery = "SELECT COUNT(*) as total FROM ts_price_calendar $whereClause";
        if (!empty($params)) {
            $stmt = $db->prepare($totalQuery);
            $stmt->bind_param($paramTypes, ...$params);
            $stmt->execute();
            $totalResult = $stmt->get_result();
            $totalRecords = $totalResult->fetch_assoc()['total'];
        } else {
            $totalResult = $db->query($totalQuery);
            $totalRecords = $totalResult->fetch_assoc()['total'];
        }

        // Zapytanie o dane z paginacją
        $dataQuery = "SELECT * FROM ts_price_calendar $whereClause ORDER BY $orderBy $orderDir LIMIT $start, $length";
        if (!empty($params)) {
            $stmt = $db->prepare($dataQuery);
            $stmt->bind_param($paramTypes, ...$params);
            $stmt->execute();
            $dataResult = $stmt->get_result();
            $data = $dataResult->fetch_all(MYSQLI_ASSOC);
        } else {
            $dataResult = $db->query($dataQuery);
            $data = $dataResult->fetch_all(MYSQLI_ASSOC);
        }

        $output = [];
        foreach ($data as $row) {
            $pc = new \App\Libs\Models\PriceCalendar();
            $pc->hydrate($row);

            $offerName = $this->getOfferName($row['id_offer']);
            $productName = $this->getProductName($row['id_offer'], $row['id_product'], $pc->ptype);
            $variantName = $this->getProductVariantName($row['id_offer'], $row['id_product'], $pc->ptype, $row['id_variant']);

            $calendar = $pc->getCalendar();
            $calendarCount = count($calendar);

            $statusLabel = \App\Libs\Models\PriceCalendar::STATUSES[$row['status']] ?? 'Nieznany';
            $statusClass = $this->getStatusClass($row['status']);

            $actions = $this->generateActionButtons($row['id'], $row['status'], $calendarCount);

            $output[] = [
                $row['id'],
                $offerName,
                $productName ?? $row['id_product'] ?? '-',
                date('Y-m-d H:i', strtotime($row['sell_date_from'])),
                $variantName ?? $row['id_variant'] ?? '-',
                "<span class='label $statusClass'>$statusLabel</span>",
                $calendarCount > 0 ? "<span class='badge badge-success'>$calendarCount</span>" : "<span class='badge badge-default'>0</span>",
                $actions
            ];
        }

        $response = [
            'draw' => $draw,
            'iTotalDisplayRecords' => $totalRecords,
            'iTotalRecords' => $totalRecords,
            'aaData' => $output,
            'sEcho' => $draw
        ];
        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }

    /**
     * Dodawanie kalendarza do produktu
     */
    public function AddCalendar()
    {
        $mgo = Mymongo::activate();
        $oferty = $mgo->mget_accepted_offers_list();
        $this->app->ADD('oferty', $oferty);
        $this->adminnavparams['selected'] = 'calendar.list';
        $this->app->ADD('section', 'calendar');
        $this->app->ADD('todo', 'add');
        $this->app->SetTemplate('modules/pricescalendar/edit_calendar.tpl');
        $this->makeWidgets();
    }

    public function EditCalendar()
    {
        $mgo = Mymongo::activate();
        $oferty = $mgo->mget_accepted_offers_list();
        $this->app->ADD('oferty', $oferty);
        $this->adminnavparams['selected'] = 'calendar.list';
        $this->app->ADD('section', 'calendar');
        $this->app->ADD('todo', 'edit');
        $item = new \App\Libs\Models\PriceCalendar();
        $item->findOne($this->app->urlparam['id']);
        $this->app->ADD('postback', $item->asArray());
        $this->app->ADD('cid', $item->id);
        $this->app->ADD('proferty', $this->getOfferProductsList($item->id_offer, $item->ptype));
        $this->app->ADD('variants', $this->getProductsVariantsList($item->id_offer, $item->ptype, $item->id_product, true));

        // Dodaj wygenerowany kalendarz jeśli istnieje
        $calendar = $item->getCalendar();
        $this->app->ADD('generated_calendar', $calendar);

        $this->app->SetTemplate('modules/pricescalendar/edit_calendar.tpl');
        $this->makeWidgets();
    }

    protected function ajaxGetOfferProductsList($offerid, $ptype)
    {
        $resp['error'] = false;
        $error = [];

        if (false === Mymongo::isValidMongoid($offerid)) {
            $error[] = $this->app->Lang('Błąd MGO', 'Promocje');
        }

        if (false === preg_match('/[a-zA-Z]/', $ptype)) {
            $error[] = $this->app->Lang('Błąd PTYPE', 'Promocje');
        }

        if (count($error) > 0) {
            $resp['error'] = true;
            $resp['msg'] = implode('\n', $error);
            echo json_encode($resp);
            die();
        }

        $products = $this->getOfferProductsList($offerid, $ptype);

        if ($products['error']) {
            echo json_encode($products);
            die();
        }

        $resp['product'] = $products;
        echo json_encode($resp);
        die();
    }

    protected function ajaxGetProductVariantsList($offerid, $ptype, $product)
    {
        $resp['error'] = false;
        $error = [];

        if (false === Mymongo::isValidMongoid($offerid)) {
            $error[] = $this->app->Lang('Błąd MGO', 'Promocje');
        }

        if (false === preg_match('/[a-zA-Z]/', $ptype)) {
            $error[] = $this->app->Lang('Błąd PTYPE', 'Promocje');
        }

        if (count($error) > 0) {
            $resp['error'] = true;
            $resp['msg'] = implode('\n', $error);
            echo json_encode($resp);
            die();
        }

        $variants = $this->getProductsVariantsList($offerid, $ptype, $product, true);

        if ($variants['error']) {
            echo json_encode($variants);
            die();
        }

        $resp['variants'] = $variants;
        echo json_encode($resp);
        die();
    }

    protected function checkProductsResponse(array $cursor, $ptype): array
    {
        $resp['error'] = false;
        if (!is_array($cursor)) {
            $resp['error'] = true;
            $resp['msg'] = $this->app->Lang('Brak produktów', 'Promocje');
        }

        if (count($cursor[$ptype] ?? []) === 0) {
            $resp['error'] = true;
            $resp['msg'] = $this->app->Lang('Brak produktów', 'Promocje');
        }

        return $resp;
    }

    protected function getOfferProductsList($offerId, $ptype, bool $full = false)
    {
        if (!($this->offersObjects[$offerId] ?? false)) {
            $this->offersObjects[$offerId] = new Offer($this->app);
        }

        return $this->offersObjects[$offerId]->getOfferProductsList($offerId, $ptype, $full);
    }

    protected function getProductsVariantsList($offerid, $ptype, $product, bool $full = false)
    {
        $check = $this->getOfferProductsList($offerid, $ptype, $full);

        if ($check['error']) {
            return $check;
        }
        $variants = [];
        foreach ($check as $v) {
            if ($v['identyfikator'] === $product) {
                foreach ($v['wariant_cenowy'] as $variant) {
                    $variants[] = [
                        'bsid' => $variant['bsid'],
                        'nazwa' => $variant['nazwa'],
                        'cena' => $variant['cena']['brutto']
                    ];
                }
            }
        }
        return $variants;
    }

    private function makeWidgets()
    {
        $this->app->AWD('header', $this->headerparams);
        $this->app->AWD('top', $this->topparams);
        $this->app->AWD('adminnav', $this->adminnavparams);
        $this->app->AWD('footer', $this->footerparams);
    }

    /**
     * Pobiera nazwę oferty na podstawie ID
     */
    private function getOfferName($offerId): string
    {
        if (empty($offerId)) {
            return '-';
        }

        if (!($this->offersObjects[$offerId] ?? false)) {
            $this->offersObjects[$offerId] = new Offer($this->app);
        }

        return $this->offersObjects[$offerId]->getOfferName($offerId);
    }

    private function getProductName($offerId, $productId, $ptype): string
    {
        if (empty($offerId) || empty($productId)) {
            return '-';
        }

        if (!($this->offersObjects[$offerId] ?? false)) {
            $this->offersObjects[$offerId] = new Offer($this->app);
        }

        return $this->offersObjects[$offerId]->getProductName($offerId, $productId, $ptype);
    }

    private function getProductVariantName($offerId, $productId, $ptype, $variantId): string
    {
        if (empty($offerId) || empty($productId) || empty($variantId)) {
            return '-';
        }

        if (!($this->offersObjects[$offerId] ?? false)) {
            $this->offersObjects[$offerId] = new Offer($this->app);
        }

        return $this->offersObjects[$offerId]->getProductVariantName($offerId, $productId, $ptype, $variantId);
    }

    /**
     * Zwraca klasę CSS dla statusu
     */
    private function getStatusClass($status): string
    {
        switch ($status) {
            case -1:
                return 'label-default';
            case 0:
                return 'label-danger';
            case 1:
                return 'label-success';
            case 2:
                return 'label-warning';
            default:
                return 'label-default';
        }
    }

    /**
     * Generuje przyciski akcji dla wiersza tabeli
     */
    private function generateActionButtons($id, $status, $calendarCount): string
    {
        $buttons = [];

        // Edycja
        $buttons[] = "<a href='admin.php?pricescalendar&a=edit&id=$id' class='btn btn-primary btn-xs' title='Edytuj'><i class='fa fa-edit'></i></a>";

        // Duplikowanie
        $buttons[] = "<a href='admin.php?pricescalendar&a=duplicate&id=$id' class='btn btn-info btn-xs' title='Duplikuj kalendarz' onclick='return confirm(\"Czy chcesz zduplikować ten kalendarz? Zostanie utworzona kopia z ustawieniem status na Nowy.\")'><i class='fa fa-copy'></i></a>";

        // Generowanie kalendarza
//        $buttons[] = "<a href='admin.php?pricescalendar&a=generate&id=$id' class='btn btn-success btn-xs' title='Generuj kalendarz' onclick='return confirm(\"Czy chcesz wygenerować kalendarz cen? Istniejące wpisy zostaną usunięte.\")'><i class='fa fa-calendar'></i></a>";

        // Czyszczenie kalendarza (tylko jeśli są wpisy)
//        if ($calendarCount > 0) {
//            $buttons[] = "<a href='admin.php?pricescalendar&a=clear&id=$id' class='btn btn-warning btn-xs' title='Wyczyść kalendarz' onclick='return confirm(\"Czy chcesz usunąć wpisy kalendarza? Konfiguracja zostanie zachowana.\")'><i class='fa fa-eraser'></i></a>";
//        }

        // Usuwanie
        $buttons[] = "<a href='admin.php?pricescalendar&a=delete&id=$id' class='btn btn-danger btn-xs' title='Usuń kalendarz' onclick='return confirm(\"Czy chcesz usunąć cały kalendarz wraz z konfiguracją? Ta operacja jest nieodwracalna.\")'><i class='fa fa-trash'></i></a>";

        return implode(' ', $buttons);
    }
}